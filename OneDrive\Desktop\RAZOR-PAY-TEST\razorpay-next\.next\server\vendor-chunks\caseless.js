/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/caseless";
exports.ids = ["vendor-chunks/caseless"];
exports.modules = {

/***/ "(rsc)/./node_modules/caseless/index.js":
/*!****************************************!*\
  !*** ./node_modules/caseless/index.js ***!
  \****************************************/
/***/ ((module) => {

eval("function Caseless (dict) {\n  this.dict = dict || {}\n}\nCaseless.prototype.set = function (name, value, clobber) {\n  if (typeof name === 'object') {\n    for (var i in name) {\n      this.set(i, name[i], value)\n    }\n  } else {\n    if (typeof clobber === 'undefined') clobber = true\n    var has = this.has(name)\n\n    if (!clobber && has) this.dict[has] = this.dict[has] + ',' + value\n    else this.dict[has || name] = value\n    return has\n  }\n}\nCaseless.prototype.has = function (name) {\n  var keys = Object.keys(this.dict)\n    , name = name.toLowerCase()\n    ;\n  for (var i=0;i<keys.length;i++) {\n    if (keys[i].toLowerCase() === name) return keys[i]\n  }\n  return false\n}\nCaseless.prototype.get = function (name) {\n  name = name.toLowerCase()\n  var result, _key\n  var headers = this.dict\n  Object.keys(headers).forEach(function (key) {\n    _key = key.toLowerCase()\n    if (name === _key) result = headers[key]\n  })\n  return result\n}\nCaseless.prototype.swap = function (name) {\n  var has = this.has(name)\n  if (has === name) return\n  if (!has) throw new Error('There is no header than matches \"'+name+'\"')\n  this.dict[name] = this.dict[has]\n  delete this.dict[has]\n}\nCaseless.prototype.del = function (name) {\n  var has = this.has(name)\n  return delete this.dict[has || name]\n}\n\nmodule.exports = function (dict) {return new Caseless(dict)}\nmodule.exports.httpify = function (resp, headers) {\n  var c = new Caseless(headers)\n  resp.setHeader = function (key, value, clobber) {\n    if (typeof value === 'undefined') return\n    return c.set(key, value, clobber)\n  }\n  resp.hasHeader = function (key) {\n    return c.has(key)\n  }\n  resp.getHeader = function (key) {\n    return c.get(key)\n  }\n  resp.removeHeader = function (key) {\n    return c.del(key)\n  }\n  resp.headers = c.dict\n  return c\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/caseless/index.js\n");

/***/ })

};
;