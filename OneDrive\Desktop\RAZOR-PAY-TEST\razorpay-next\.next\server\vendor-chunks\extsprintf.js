/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/extsprintf";
exports.ids = ["vendor-chunks/extsprintf"];
exports.modules = {

/***/ "(rsc)/./node_modules/extsprintf/lib/extsprintf.js":
/*!***************************************************!*\
  !*** ./node_modules/extsprintf/lib/extsprintf.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/*\n * extsprintf.js: extended POSIX-style sprintf\n */\n\nvar mod_assert = __webpack_require__(/*! assert */ \"assert\");\nvar mod_util = __webpack_require__(/*! util */ \"util\");\n\n/*\n * Public interface\n */\nexports.sprintf = jsSprintf;\nexports.printf = jsPrintf;\nexports.fprintf = jsFprintf;\n\n/*\n * Stripped down version of s[n]printf(3c).  We make a best effort to throw an\n * exception when given a format string we don't understand, rather than\n * ignoring it, so that we won't break existing programs if/when we go implement\n * the rest of this.\n *\n * This implementation currently supports specifying\n *\t- field alignment ('-' flag),\n * \t- zero-pad ('0' flag)\n *\t- always show numeric sign ('+' flag),\n *\t- field width\n *\t- conversions for strings, decimal integers, and floats (numbers).\n *\t- argument size specifiers.  These are all accepted but ignored, since\n *\t  Javascript has no notion of the physical size of an argument.\n *\n * Everything else is currently unsupported, most notably precision, unsigned\n * numbers, non-decimal numbers, and characters.\n */\nfunction jsSprintf(fmt)\n{\n\tvar regex = [\n\t    '([^%]*)',\t\t\t\t/* normal text */\n\t    '%',\t\t\t\t/* start of format */\n\t    '([\\'\\\\-+ #0]*?)',\t\t\t/* flags (optional) */\n\t    '([1-9]\\\\d*)?',\t\t\t/* width (optional) */\n\t    '(\\\\.([1-9]\\\\d*))?',\t\t/* precision (optional) */\n\t    '[lhjztL]*?',\t\t\t/* length mods (ignored) */\n\t    '([diouxXfFeEgGaAcCsSp%jr])'\t/* conversion */\n\t].join('');\n\n\tvar re = new RegExp(regex);\n\tvar args = Array.prototype.slice.call(arguments, 1);\n\tvar flags, width, precision, conversion;\n\tvar left, pad, sign, arg, match;\n\tvar ret = '';\n\tvar argn = 1;\n\n\tmod_assert.equal('string', typeof (fmt));\n\n\twhile ((match = re.exec(fmt)) !== null) {\n\t\tret += match[1];\n\t\tfmt = fmt.substring(match[0].length);\n\n\t\tflags = match[2] || '';\n\t\twidth = match[3] || 0;\n\t\tprecision = match[4] || '';\n\t\tconversion = match[6];\n\t\tleft = false;\n\t\tsign = false;\n\t\tpad = ' ';\n\n\t\tif (conversion == '%') {\n\t\t\tret += '%';\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (args.length === 0)\n\t\t\tthrow (new Error('too few args to sprintf'));\n\n\t\targ = args.shift();\n\t\targn++;\n\n\t\tif (flags.match(/[\\' #]/))\n\t\t\tthrow (new Error(\n\t\t\t    'unsupported flags: ' + flags));\n\n\t\tif (precision.length > 0)\n\t\t\tthrow (new Error(\n\t\t\t    'non-zero precision not supported'));\n\n\t\tif (flags.match(/-/))\n\t\t\tleft = true;\n\n\t\tif (flags.match(/0/))\n\t\t\tpad = '0';\n\n\t\tif (flags.match(/\\+/))\n\t\t\tsign = true;\n\n\t\tswitch (conversion) {\n\t\tcase 's':\n\t\t\tif (arg === undefined || arg === null)\n\t\t\t\tthrow (new Error('argument ' + argn +\n\t\t\t\t    ': attempted to print undefined or null ' +\n\t\t\t\t    'as a string'));\n\t\t\tret += doPad(pad, width, left, arg.toString());\n\t\t\tbreak;\n\n\t\tcase 'd':\n\t\t\targ = Math.floor(arg);\n\t\t\t/*jsl:fallthru*/\n\t\tcase 'f':\n\t\t\tsign = sign && arg > 0 ? '+' : '';\n\t\t\tret += sign + doPad(pad, width, left,\n\t\t\t    arg.toString());\n\t\t\tbreak;\n\n\t\tcase 'x':\n\t\t\tret += doPad(pad, width, left, arg.toString(16));\n\t\t\tbreak;\n\n\t\tcase 'j': /* non-standard */\n\t\t\tif (width === 0)\n\t\t\t\twidth = 10;\n\t\t\tret += mod_util.inspect(arg, false, width);\n\t\t\tbreak;\n\n\t\tcase 'r': /* non-standard */\n\t\t\tret += dumpException(arg);\n\t\t\tbreak;\n\n\t\tdefault:\n\t\t\tthrow (new Error('unsupported conversion: ' +\n\t\t\t    conversion));\n\t\t}\n\t}\n\n\tret += fmt;\n\treturn (ret);\n}\n\nfunction jsPrintf() {\n\tvar args = Array.prototype.slice.call(arguments);\n\targs.unshift(process.stdout);\n\tjsFprintf.apply(null, args);\n}\n\nfunction jsFprintf(stream) {\n\tvar args = Array.prototype.slice.call(arguments, 1);\n\treturn (stream.write(jsSprintf.apply(this, args)));\n}\n\nfunction doPad(chr, width, left, str)\n{\n\tvar ret = str;\n\n\twhile (ret.length < width) {\n\t\tif (left)\n\t\t\tret += chr;\n\t\telse\n\t\t\tret = chr + ret;\n\t}\n\n\treturn (ret);\n}\n\n/*\n * This function dumps long stack traces for exceptions having a cause() method.\n * See node-verror for an example.\n */\nfunction dumpException(ex)\n{\n\tvar ret;\n\n\tif (!(ex instanceof Error))\n\t\tthrow (new Error(jsSprintf('invalid type for %%r: %j', ex)));\n\n\t/* Note that V8 prepends \"ex.stack\" with ex.toString(). */\n\tret = 'EXCEPTION: ' + ex.constructor.name + ': ' + ex.stack;\n\n\tif (ex.cause && typeof (ex.cause) === 'function') {\n\t\tvar cex = ex.cause();\n\t\tif (cex) {\n\t\t\tret += '\\nCaused by: ' + dumpException(cex);\n\t\t}\n\t}\n\n\treturn (ret);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/extsprintf/lib/extsprintf.js\n");

/***/ })

};
;