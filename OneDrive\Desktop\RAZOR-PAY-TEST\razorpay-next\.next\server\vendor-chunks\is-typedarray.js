/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-typedarray";
exports.ids = ["vendor-chunks/is-typedarray"];
exports.modules = {

/***/ "(rsc)/./node_modules/is-typedarray/index.js":
/*!*********************************************!*\
  !*** ./node_modules/is-typedarray/index.js ***!
  \*********************************************/
/***/ ((module) => {

eval("module.exports      = isTypedArray\nisTypedArray.strict = isStrictTypedArray\nisTypedArray.loose  = isLooseTypedArray\n\nvar toString = Object.prototype.toString\nvar names = {\n    '[object Int8Array]': true\n  , '[object Int16Array]': true\n  , '[object Int32Array]': true\n  , '[object Uint8Array]': true\n  , '[object Uint8ClampedArray]': true\n  , '[object Uint16Array]': true\n  , '[object Uint32Array]': true\n  , '[object Float32Array]': true\n  , '[object Float64Array]': true\n}\n\nfunction isTypedArray(arr) {\n  return (\n       isStrictTypedArray(arr)\n    || isLooseTypedArray(arr)\n  )\n}\n\nfunction isStrictTypedArray(arr) {\n  return (\n       arr instanceof Int8Array\n    || arr instanceof Int16Array\n    || arr instanceof Int32Array\n    || arr instanceof Uint8Array\n    || arr instanceof Uint8ClampedArray\n    || arr instanceof Uint16Array\n    || arr instanceof Uint32Array\n    || arr instanceof Float32Array\n    || arr instanceof Float64Array\n  )\n}\n\nfunction isLooseTypedArray(arr) {\n  return names[toString.call(arr)]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaXMtdHlwZWRhcnJheS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYXpvcnBheS1uZXh0Ly4vbm9kZV9tb2R1bGVzL2lzLXR5cGVkYXJyYXkvaW5kZXguanM/MGI1YyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyAgICAgID0gaXNUeXBlZEFycmF5XG5pc1R5cGVkQXJyYXkuc3RyaWN0ID0gaXNTdHJpY3RUeXBlZEFycmF5XG5pc1R5cGVkQXJyYXkubG9vc2UgID0gaXNMb29zZVR5cGVkQXJyYXlcblxudmFyIHRvU3RyaW5nID0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZ1xudmFyIG5hbWVzID0ge1xuICAgICdbb2JqZWN0IEludDhBcnJheV0nOiB0cnVlXG4gICwgJ1tvYmplY3QgSW50MTZBcnJheV0nOiB0cnVlXG4gICwgJ1tvYmplY3QgSW50MzJBcnJheV0nOiB0cnVlXG4gICwgJ1tvYmplY3QgVWludDhBcnJheV0nOiB0cnVlXG4gICwgJ1tvYmplY3QgVWludDhDbGFtcGVkQXJyYXldJzogdHJ1ZVxuICAsICdbb2JqZWN0IFVpbnQxNkFycmF5XSc6IHRydWVcbiAgLCAnW29iamVjdCBVaW50MzJBcnJheV0nOiB0cnVlXG4gICwgJ1tvYmplY3QgRmxvYXQzMkFycmF5XSc6IHRydWVcbiAgLCAnW29iamVjdCBGbG9hdDY0QXJyYXldJzogdHJ1ZVxufVxuXG5mdW5jdGlvbiBpc1R5cGVkQXJyYXkoYXJyKSB7XG4gIHJldHVybiAoXG4gICAgICAgaXNTdHJpY3RUeXBlZEFycmF5KGFycilcbiAgICB8fCBpc0xvb3NlVHlwZWRBcnJheShhcnIpXG4gIClcbn1cblxuZnVuY3Rpb24gaXNTdHJpY3RUeXBlZEFycmF5KGFycikge1xuICByZXR1cm4gKFxuICAgICAgIGFyciBpbnN0YW5jZW9mIEludDhBcnJheVxuICAgIHx8IGFyciBpbnN0YW5jZW9mIEludDE2QXJyYXlcbiAgICB8fCBhcnIgaW5zdGFuY2VvZiBJbnQzMkFycmF5XG4gICAgfHwgYXJyIGluc3RhbmNlb2YgVWludDhBcnJheVxuICAgIHx8IGFyciBpbnN0YW5jZW9mIFVpbnQ4Q2xhbXBlZEFycmF5XG4gICAgfHwgYXJyIGluc3RhbmNlb2YgVWludDE2QXJyYXlcbiAgICB8fCBhcnIgaW5zdGFuY2VvZiBVaW50MzJBcnJheVxuICAgIHx8IGFyciBpbnN0YW5jZW9mIEZsb2F0MzJBcnJheVxuICAgIHx8IGFyciBpbnN0YW5jZW9mIEZsb2F0NjRBcnJheVxuICApXG59XG5cbmZ1bmN0aW9uIGlzTG9vc2VUeXBlZEFycmF5KGFycikge1xuICByZXR1cm4gbmFtZXNbdG9TdHJpbmcuY2FsbChhcnIpXVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/is-typedarray/index.js\n");

/***/ })

};
;