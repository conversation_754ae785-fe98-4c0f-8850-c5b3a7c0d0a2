### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [v1.0.7](https://github.com/ljharb/internal-slot/compare/v1.0.6...v1.0.7)

> 5 February 2024

- [<PERSON>] update `aud`, `npmignore`, `tape` [`89c88c1`](https://github.com/ljharb/internal-slot/commit/89c88c1ed8de7c681fd3cec7bb2f045db0268d84)
- [Refactor] use `es-errors`, so things that only need those do not need `get-intrinsic` [`b437631`](https://github.com/ljharb/internal-slot/commit/b4376312d4a5d7bc99fb383cae3f15bd2f3d36d1)

#### [v1.0.6](https://github.com/ljharb/internal-slot/compare/v1.0.5...v1.0.6)

> 20 October 2023

- [<PERSON>] update `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`4d568d2`](https://github.com/ljharb/internal-slot/commit/4d568d2897a2efe9b0604ae240bc89787924070f)
- [Refactor] use `hasown` instead of `has` [`f946e94`](https://github.com/ljharb/internal-slot/commit/f946e94885f5fa092a4de04f366d746c0c5a2f2f)
- [Deps] update `get-intrinsic` [`1bbc885`](https://github.com/ljharb/internal-slot/commit/1bbc885b0225dadac6e50f421cda5814c242b0bb)
- [meta] remove unused `.eslintignore` [`6fdde1a`](https://github.com/ljharb/internal-slot/commit/6fdde1a25348cf9fc41c9808d342e6502f37658d)

#### [v1.0.5](https://github.com/ljharb/internal-slot/compare/v1.0.4...v1.0.5)

> 9 February 2023

- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`e427703`](https://github.com/ljharb/internal-slot/commit/e427703bfc669c590a863ec77ecd3789d7b7c458)
- [Deps] update `get-intrinsic` [`aa652f0`](https://github.com/ljharb/internal-slot/commit/aa652f05c5c15b4ed1a118be60f0565e47bd7208)
- [Fix] improve assertion message [`8df86e3`](https://github.com/ljharb/internal-slot/commit/8df86e3ea21786b5eb7654f22202665c8b63accf)

#### [v1.0.4](https://github.com/ljharb/internal-slot/compare/v1.0.3...v1.0.4)

> 13 December 2022

- [actions] reuse common workflows [`82a1aee`](https://github.com/ljharb/internal-slot/commit/82a1aee603bce8627930597edb3a04b4970ed151)
- [meta] use `npmignore` to autogenerate an npmignore file [`56f7e71`](https://github.com/ljharb/internal-slot/commit/56f7e7182dd934dd6c1b80497a110670d02a91b9)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`e25ff67`](https://github.com/ljharb/internal-slot/commit/e25ff67d568f77c1b66168957d82b080779e1c0a)
- [actions] update rebase action to use reusable workflow [`227e81e`](https://github.com/ljharb/internal-slot/commit/227e81eaef7230a265103ef1ef0618d2920c3f30)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `foreach`, `object-inspect`, `tape` [`fc9f319`](https://github.com/ljharb/internal-slot/commit/fc9f319d136ddf2e79910390d1e7ad279d41cc01)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `object-inspect`, `safe-publish-latest`, `tape` [`0a72a0f`](https://github.com/ljharb/internal-slot/commit/0a72a0f389511b41645f441da19257a266cb37f7)
- [actions] update codecov uploader [`e2b993f`](https://github.com/ljharb/internal-slot/commit/e2b993f143278a30424ebd5526019e59828989d0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`8f0ab80`](https://github.com/ljharb/internal-slot/commit/8f0ab808afdd458001c35c828962dc714d824754)
- [actions] update checkout action [`8da4b91`](https://github.com/ljharb/internal-slot/commit/8da4b91c3454671da2e53a831ca0928147965a09)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@safe-publish-latest`, `tape` [`7ab37aa`](https://github.com/ljharb/internal-slot/commit/7ab37aabf01ded2605fa583a9866b62172f82e30)
- [readme] add github actions/codecov badges [`71234be`](https://github.com/ljharb/internal-slot/commit/71234bef4ef99e2f17d72ae3a1b7c0522519b7d7)
- [Fix] `assert`: throw on a nonexistent slot even when an object already has other slots [`12580bd`](https://github.com/ljharb/internal-slot/commit/12580bd26fe9f8603566e9e076092b5e1fb7340b)
- [Tests] use `for-each` instead of `foreach` [`7229df0`](https://github.com/ljharb/internal-slot/commit/7229df01666ccb022dde82686d84b97b7bcfc53a)
- [meta] use `prepublishOnly` script for npm 7+ [`8728872`](https://github.com/ljharb/internal-slot/commit/8728872cfbd735d3ae87e885c081a08d5b26edf0)
- [Deps] update `get-intrinsic` [`1b7088f`](https://github.com/ljharb/internal-slot/commit/1b7088fa970c33757816b08357814bdbf6d722b6)
- [Deps] update `get-intrinsic` [`063621e`](https://github.com/ljharb/internal-slot/commit/063621ec99d1b9262d3898c0ecad0e1e98be5f75)

#### [v1.0.3](https://github.com/ljharb/internal-slot/compare/v1.0.2...v1.0.3)

> 26 January 2021

- [Tests] use shared travis-ci configs [`0ef2263`](https://github.com/ljharb/internal-slot/commit/0ef22634fa2269d9df0d784aca3c5748e8eabd3b)
- [Tests] migrate tests to Github Actions [`6253915`](https://github.com/ljharb/internal-slot/commit/6253915d28721df2eda5630849bc6b57647e3ee2)
- [meta] do not publish github action workflow files [`ef94e55`](https://github.com/ljharb/internal-slot/commit/ef94e555727ed6a649ef64010904fe89a468d459)
- [Tests] run `nyc` on all tests; use `tape` runner [`917d6ca`](https://github.com/ljharb/internal-slot/commit/917d6ca630cdcd6b4da9a2c300c6a3abb6e724fe)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`8dcb6fe`](https://github.com/ljharb/internal-slot/commit/8dcb6fe01d6a45e1af17a9dace95ca47c99b4328)
- [actions] add "Allow Edits" workflow [`7aa3e86`](https://github.com/ljharb/internal-slot/commit/7aa3e86edb0149fd882717481885760aeb28474e)
- [Refactor] use `get-intrinsic` instead of `es-abstract`; update `side-channel` [`11ad17d`](https://github.com/ljharb/internal-slot/commit/11ad17d4255adcbc55fd4eca0bf6733bac59f1bf)
- [readme] remove travis badge [`5b75452`](https://github.com/ljharb/internal-slot/commit/5b754523aa07e8f67d0135df75059a18047292bb)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`d531688`](https://github.com/ljharb/internal-slot/commit/d5316880956b4dd83e6b6c9ab48fdd8171a4a268)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`c76fa91`](https://github.com/ljharb/internal-slot/commit/c76fa91a7e623a738e22332bee4e985aea41122e)
- [Dev Deps] update `eslint`, `tape` [`e733ccd`](https://github.com/ljharb/internal-slot/commit/e733ccd68e81c72ef2e02726e001895053de7887)
- [Dev Deps] update `auto-changelog`; add `aud` [`df20bf5`](https://github.com/ljharb/internal-slot/commit/df20bf5d3943a533c20799d8cc1449997e85d53b)
- [meta] fix autochangelog [`e89e6f1`](https://github.com/ljharb/internal-slot/commit/e89e6f1ff9f10f386e6400b586db78ad9c0f1309)
- [Tests] only audit prod deps [`71317b9`](https://github.com/ljharb/internal-slot/commit/71317b95ec6bbd9877807da0c0316ee9f5f30fab)
- [Deps] update `es-abstract` [`c17ccf4`](https://github.com/ljharb/internal-slot/commit/c17ccf45f4cb0d3b7a1536e9cd3a7ff9a7dafd21)
- [Dev Deps] update `tape` [`d81ae03`](https://github.com/ljharb/internal-slot/commit/d81ae030a0e8f58cee00f752601ce60405a93d78)
- [Deps] update `es-abstract` [`b56303b`](https://github.com/ljharb/internal-slot/commit/b56303b4c3af7a510f9f51860895a46fd2e14752)
- [Deps] update `es-abstract` [`9996d1c`](https://github.com/ljharb/internal-slot/commit/9996d1cf3507750c7a6845a2fb0d0f849ea898a1)

#### [v1.0.2](https://github.com/ljharb/internal-slot/compare/v1.0.1...v1.0.2)

> 20 December 2019

- [Deps] update `es-abstract`, `side-channel` [`5c9df03`](https://github.com/ljharb/internal-slot/commit/5c9df03a25518f5c482cff4e1447a26fa071df9a)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`7820f98`](https://github.com/ljharb/internal-slot/commit/7820f984e523a64ddf3068c4e5631abf61eb1ea4)

#### [v1.0.1](https://github.com/ljharb/internal-slot/compare/v1.0.0...v1.0.1)

> 1 December 2019

- [Refactor] use `side-channel` package [`d38639f`](https://github.com/ljharb/internal-slot/commit/d38639f0a3cdb5090711179d0e78df857ecbd5d3)
- [actions] add automatic rebasing / merge commit blocking [`74267e6`](https://github.com/ljharb/internal-slot/commit/74267e6e591e18ba39186cb99139d3fd7a757c9f)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `object-inspect`, `safe-publish-latest` [`b042eef`](https://github.com/ljharb/internal-slot/commit/b042eefc067b830bbd370833f7f21754e802b0b2)
- [Deps] update `es-abstract` [`98cf4e8`](https://github.com/ljharb/internal-slot/commit/98cf4e86c1bfe99eda7b11a8ea70394368f33e4f)

#### v1.0.0

> 20 October 2019

- Tests [`b50fa41`](https://github.com/ljharb/internal-slot/commit/b50fa41b6f47aba39ac4cb733658580974a0b00a)
- implementation [`c5a59f3`](https://github.com/ljharb/internal-slot/commit/c5a59f3753677f81aa12a0226d3b1187846d06dd)
- Initial commit [`15ebe4d`](https://github.com/ljharb/internal-slot/commit/15ebe4dc6d46885f67969d64c9c3e705780963f8)
- readme [`382a3f5`](https://github.com/ljharb/internal-slot/commit/382a3f53d8975e6488373a0fc2abcdc7c4c44247)
- npm init [`d5e7c97`](https://github.com/ljharb/internal-slot/commit/d5e7c977ef694e89c245fd11165f63c06a7a5040)
- [meta] add FUNDING.yml [`685b608`](https://github.com/ljharb/internal-slot/commit/685b6087613f6735f4411a558500d92f8a3ec3f2)
- [meta] add `auto-changelog`, `safe-publish-latest` [`f8fdf1c`](https://github.com/ljharb/internal-slot/commit/f8fdf1c3f0c592f71746da6d7f8bea18f8946dda)
- [Tests] add `npm run lint` [`baaaa09`](https://github.com/ljharb/internal-slot/commit/baaaa09ab6e5bc5fcc0e7c76e10c55aa18f4ca7e)
- Only apps should have lockfiles [`dfa7efa`](https://github.com/ljharb/internal-slot/commit/dfa7efa3d5cd23261cb75c2adab6ee3c06790fee)
