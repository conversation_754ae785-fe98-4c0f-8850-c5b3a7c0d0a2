{"name": "is-symbol", "version": "1.0.4", "description": "Determine if a value is an ES6 Symbol or not.", "main": "index.js", "scripts": {"prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx aud --production", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-symbol.git"}, "keywords": ["symbol", "es6", "is", "Symbol"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-symbol/issues"}, "dependencies": {"has-symbols": "^1.0.2"}, "devDependencies": {"@ljharb/eslint-config": "^17.6.0", "aud": "^1.1.5", "auto-changelog": "^2.2.1", "eslint": "^7.26.0", "nyc": "^10.3.2", "object-inspect": "^1.10.3", "safe-publish-latest": "^1.1.4", "tape": "^5.2.2"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}}