"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-json-stable-stringify";
exports.ids = ["vendor-chunks/fast-json-stable-stringify"];
exports.modules = {

/***/ "(rsc)/./node_modules/fast-json-stable-stringify/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/fast-json-stable-stringify/index.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = function (data, opts) {\n    if (!opts) opts = {};\n    if (typeof opts === 'function') opts = { cmp: opts };\n    var cycles = (typeof opts.cycles === 'boolean') ? opts.cycles : false;\n\n    var cmp = opts.cmp && (function (f) {\n        return function (node) {\n            return function (a, b) {\n                var aobj = { key: a, value: node[a] };\n                var bobj = { key: b, value: node[b] };\n                return f(aobj, bobj);\n            };\n        };\n    })(opts.cmp);\n\n    var seen = [];\n    return (function stringify (node) {\n        if (node && node.toJSON && typeof node.toJSON === 'function') {\n            node = node.toJSON();\n        }\n\n        if (node === undefined) return;\n        if (typeof node == 'number') return isFinite(node) ? '' + node : 'null';\n        if (typeof node !== 'object') return JSON.stringify(node);\n\n        var i, out;\n        if (Array.isArray(node)) {\n            out = '[';\n            for (i = 0; i < node.length; i++) {\n                if (i) out += ',';\n                out += stringify(node[i]) || 'null';\n            }\n            return out + ']';\n        }\n\n        if (node === null) return 'null';\n\n        if (seen.indexOf(node) !== -1) {\n            if (cycles) return JSON.stringify('__cycle__');\n            throw new TypeError('Converting circular structure to JSON');\n        }\n\n        var seenIndex = seen.push(node) - 1;\n        var keys = Object.keys(node).sort(cmp && cmp(node));\n        out = '';\n        for (i = 0; i < keys.length; i++) {\n            var key = keys[i];\n            var value = stringify(node[key]);\n\n            if (!value) continue;\n            if (out) out += ',';\n            out += JSON.stringify(key) + ':' + value;\n        }\n        seen.splice(seenIndex, 1);\n        return '{' + out + '}';\n    })(data);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-json-stable-stringify/index.js\n");

/***/ })

};
;