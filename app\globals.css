@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 262 83% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-gradient-to-br from-slate-50 via-white to-purple-50 text-foreground min-h-screen;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.8);
  }
}

@keyframes slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scale-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, hsl(262, 83%, 58%), hsl(310, 100%, 69%));
}

.gradient-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.7)
  );
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Interactive elements */
.interactive-card {
  @apply transition-all duration-500 hover:scale-105 hover:shadow-2xl cursor-pointer transform-gpu;
}

.interactive-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(168, 85, 247, 0.15);
}

/* Button enhancements */
.btn-gradient {
  @apply bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600 hover:from-purple-700 hover:via-purple-800 hover:to-pink-700 text-white font-semibold py-4 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl relative overflow-hidden;
}

.btn-gradient::before {
  content: "";
  @apply absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300;
}

.btn-gradient:hover::before {
  @apply opacity-100;
}

/* Pricing card enhancements */
.pricing-card {
  @apply relative overflow-hidden rounded-3xl border border-gray-200/50 bg-white/80 backdrop-blur-xl p-8 shadow-xl transition-all duration-500 hover:shadow-sm hover:scale-75 transform-gpu;
}

.pricing-card.popular {
  @apply border-purple-500/50 ring-2 ring-purple-500/30 bg-gradient-to-br from-purple-50/80 to-pink-50/80;
}

.pricing-card.popular::before {
  content: "✨ Most Popular";
  @apply absolute -top-1 -right-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 text-sm font-bold rounded-bl-2xl rounded-tr-3xl shadow-lg;
}

/* Form enhancements */
.form-input {
  @apply w-full px-6 py-4 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 transition-all duration-300 bg-white/80 backdrop-blur-sm;
}

.form-input:focus {
  box-shadow: 0 0 0 4px rgba(168, 85, 247, 0.1);
  transform: translateY(-2px);
}

/* Progress indicators */
.progress-step {
  @apply flex items-center justify-center w-12 h-12 rounded-full border-2 border-gray-300 bg-white text-gray-500 font-bold transition-all duration-300 shadow-lg;
}

.progress-step.active {
  @apply border-purple-500 bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-xl;
}

.progress-step.completed {
  @apply border-green-500 bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-xl;
}

/* Loading animations */
.loading-pulse {
  @apply animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%];
  animation: shimmer 1.5s infinite;
}

/* Success animations */
.success-checkmark {
  @apply w-20 h-20 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center shadow-xl;
  animation: scale-in 0.6s ease-out;
}

.success-checkmark::after {
  content: "✓";
  @apply text-white text-3xl font-bold;
  animation: slide-up 0.4s ease-out 0.3s both;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .interactive-card:hover {
    transform: translateY(-4px) scale(1.01);
  }

  .pricing-card:hover {
    transform: translateY(-4px) scale(1.01);
  }

  body {
    @apply bg-gradient-to-b from-slate-50 to-purple-50;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-gradient-to-b from-purple-500 to-pink-500 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply from-purple-600 to-pink-600;
}
