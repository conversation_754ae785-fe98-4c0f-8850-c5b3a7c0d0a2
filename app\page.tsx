"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle2, LoaderCircle, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { apiClient, authStorage, Plan } from "@/lib/api";

type PricingCardProps = {
  plan: Plan;
  onSelectPlan: (planId: string, durationId: string, price: number) => void;
};

const PricingCard = ({ plan, onSelectPlan }: PricingCardProps) => {
  const [selectedDurationIndex, setSelectedDurationIndex] = useState(0);
  const selectedDuration = plan.durations[selectedDurationIndex];

  return (
    <Card className="max-w-96 space-y-6">
      <CardHeader className="pb-4 pt-6">
        <CardTitle className="text-2xl">{plan.name}</CardTitle>
        <CardDescription className="text-base">
          {plan.description}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Duration Selector */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Choose Duration:</Label>
          <div className="grid grid-cols-1 gap-2">
            {plan.durations.map((duration, index) => (
              <button
                key={duration.id}
                onClick={() => setSelectedDurationIndex(index)}
                className={`p-3 rounded-lg border text-left transition-all ${
                  selectedDurationIndex === index
                    ? "border-primary bg-primary/5 ring-1 ring-primary"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium">{duration.label}</span>
                  <span className="text-lg font-bold">
                    ₹{duration.price.toLocaleString()}
                  </span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {duration.valueInDays} days • {duration.paymentType}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Selected Duration Details */}
        <div className="space-y-4">
          <div className="text-center py-4 border-t border-b">
            <div className="text-3xl font-bold">
              ₹{selectedDuration.price.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">
              {selectedDuration.label} • {selectedDuration.paymentType}
            </div>
          </div>

          {/* Features List */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">What&apos;s included:</Label>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {selectedDuration.features.map((feature) => (
                <div
                  key={feature.id}
                  className="flex items-start gap-3 text-sm"
                >
                  <CheckCircle2 className="text-green-500 h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">{feature.name}</div>
                    <div className="text-muted-foreground text-xs">
                      {feature.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Select Button */}
        <Button
          className="w-full h-12 text-base font-medium"
          onClick={() =>
            onSelectPlan(plan.id, selectedDuration.id, selectedDuration.price)
          }
        >
          Select {selectedDuration.label} Plan
        </Button>
      </CardContent>
    </Card>
  );
};

export default function HomePage() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const auth = authStorage.getAuth();
    if (!auth || !authStorage.isAuthenticated()) {
      router.push("/login");
      return;
    }
    setUser(auth);
    fetchPlans(auth.accessToken);
  }, [router]);

  const fetchPlans = async (token: string) => {
    try {
      setLoading(true);
      const plansData = await apiClient.getPlans(token);
      setPlans(plansData);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch plans"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPlan = (
    planId: string,
    durationId: string,
    price: number
  ) => {
    router.push(
      `/checkout?planId=${planId}&durationId=${durationId}&price=${price}`
    );
  };

  const handleLogout = () => {
    authStorage.clearAuth();
    router.push("/login");
  };

  if (loading) {
    return (
      <div className="container h-screen flex justify-center items-center">
        <LoaderCircle className="animate-spin h-20 w-20 text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container h-screen flex flex-col justify-center items-center gap-4">
        <p className="text-red-500">{error}</p>
        <Button onClick={() => fetchPlans(user?.accessToken)}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="container py-8 flex flex-col items-center justify-center text-center">
      <div className="w-full flex justify-between items-center mb-8">
        <div>
          <h1 className="scroll-m-20 text-4xl font-extrabold tracking-tight">
            Pricing Plans
          </h1>
          <p className="text-muted-foreground">Welcome, {user?.firstName}!</p>
        </div>
        <Button variant="outline" onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </Button>
      </div>

      <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight mb-8">
        Choose the plan that&apos;s right for you
      </h2>

      <section className="flex flex-col sm:flex-row sm:flex-wrap justify-center gap-8">
        {plans.map((plan) => (
          <PricingCard
            key={plan.id}
            plan={plan}
            onSelectPlan={handleSelectPlan}
          />
        ))}
      </section>
    </div>
  );
}
