"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  CheckCircle2,
  LoaderCircle,
  Star,
  Zap,
  Trophy,
  ArrowR<PERSON>,
  Sparkles,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { apiClient, authStorage, Plan } from "@/lib/api";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

type PricingCardProps = {
  plan: Plan;
  onSelectPlan: (planId: string, durationId: string, price: number) => void;
};

const PricingCard = ({ plan, onSelectPlan }: PricingCardProps) => {
  const [selectedDurationIndex, setSelectedDurationIndex] = useState(0); // Default to first duration
  const selectedDuration = plan.durations?.[selectedDurationIndex];
  const isPopular = selectedDurationIndex === 1; // 6 months is most popular

  // Safety check - if no duration is selected or available, return null
  if (!selectedDuration || !plan.durations?.length) {
    return (
      <div className="pricing-card animate-slide-up">
        <div className="text-center p-8">
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            Plan Unavailable
          </h3>
          <p className="text-gray-600">This plan is currently not available.</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`pricing-card ${isPopular ? "popular" : ""} animate-slide-up`}
    >
      {/* Header */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-3">
          <Trophy className="h-8 w-8 text-purple-600 mr-2" />
          <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
        </div>
        <p className="text-gray-600 leading-relaxed">{plan.description}</p>
      </div>

      {/* Duration Selector */}
      <div className="mb-6">
        <div className="flex items-center justify-center mb-4">
          <Sparkles className="h-4 w-4 text-purple-600 mr-2" />
          <span className="text-sm font-medium text-gray-700">
            Choose Your Journey:
          </span>
        </div>
        <div className="grid grid-cols-1 gap-3">
          {plan.durations.map((duration, index) => (
            <button
              key={duration.id}
              onClick={() => setSelectedDurationIndex(index)}
              className={`p-4 rounded-2xl border-2 text-left transition-all duration-300 transform ${
                selectedDurationIndex === index
                  ? "border-purple-500 bg-gradient-to-r from-purple-50 to-pink-50 shadow-sm ring-2 ring-purple-200"
                  : "border-gray-200 hover:border-purple-300 hover:shadow-sm bg-white"
              }`}
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  {index === 1 && (
                    <Star className="h-4 w-4 text-yellow-500 mr-2" />
                  )}
                  <span className="font-semibold text-gray-900">
                    {duration.label}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-xl font-bold text-purple-600">
                    ₹{(duration.price || 0).toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">
                    ₹
                    {Math.round(
                      (duration.price || 0) / (duration.valueInDays || 1)
                    )}
                    /day
                  </div>
                </div>
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {duration.valueInDays} days • {duration.paymentType}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Price Display */}
      <div className="text-center mb-6 p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl border border-purple-200/50">
        <div className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
          ₹{(selectedDuration.price || 0).toLocaleString()}
        </div>
        <div className="text-sm text-gray-600 mt-1">
          {selectedDuration.label} •{" "}
          {selectedDuration.paymentType || "one-time"}
        </div>
        <div className="text-xs text-purple-600 font-medium mt-2">
          Save ₹{Math.round((selectedDuration.price || 0) * 0.2)} compared to
          monthly
        </div>
      </div>

      {/* Features List */}
      <div className="mb-8">
        <div className="flex items-center justify-center mb-4">
          <Zap className="h-4 w-4 text-purple-600 mr-2" />
          <span className="text-sm font-medium text-gray-700">
            What's Included:
          </span>
        </div>
        <div className="space-y-3 max-h-48 overflow-y-auto">
          {selectedDuration.features.map((feature, index) => (
            <div
              key={feature.id}
              className="flex items-start gap-3 p-3 rounded-xl bg-gray-50/50 hover:bg-purple-50/50 transition-colors duration-300"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CheckCircle2 className="text-green-500 h-5 w-5 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-gray-900">{feature.name}</div>
                <div className="text-gray-600 text-sm leading-relaxed">
                  {feature.description}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Select Button */}
      <button
        className="btn-gradient w-full group"
        onClick={() =>
          onSelectPlan(
            plan.id,
            selectedDuration.id,
            selectedDuration.price || 0
          )
        }
      >
        <span className="flex items-center justify-center">
          Get Started with {selectedDuration.label}
          <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
        </span>
      </button>
    </div>
  );
};

export default function HomePage() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const auth = authStorage.getAuth();
    if (!auth || !authStorage.isAuthenticated()) {
      router.push("/login");
      return;
    }
    setUser(auth);
    fetchPlans(auth.accessToken);
  }, []); // Remove router dependency to prevent multiple calls

  const fetchPlans = async (token: string) => {
    try {
      setLoading(true);
      const plansData = await apiClient.getPlans(token);
      setPlans(plansData);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch plans"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPlan = (
    planId: string,
    durationId: string,
    price: number
  ) => {
    router.push(
      `/order-summary?planId=${planId}&durationId=${durationId}&price=${price}`
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
        <Header user={user} />
        <div className="flex justify-center items-center h-[80vh]">
          <div className="text-center">
            <div className="animate-pulse-glow mb-4">
              <LoaderCircle className="animate-spin h-16 w-16 text-purple-600 mx-auto" />
            </div>
            <p className="text-gray-600 font-medium">
              Loading your fitness plans...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
        <Header user={user} />
        <div className="flex flex-col justify-center items-center h-[80vh] gap-6">
          <div className="text-center max-w-md">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-2xl">⚠️</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Oops! Something went wrong
            </h2>
            <p className="text-red-600 mb-6">{error}</p>
            <Button
              onClick={() => fetchPlans(user?.accessToken)}
              className="btn-gradient"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
      <Header user={user} />

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center max-w-4xl mx-auto">
            <div className="animate-slide-up">
              <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600 bg-clip-text text-transparent mb-6">
                Transform Your
                <span className="block">Fitness Journey</span>
              </h1>
              <p className="text-base md:text-xl text-gray-600 mb-8 leading-relaxed">
                Choose from our expertly crafted workout plans designed to help
                you achieve your fitness goals faster than ever before.
              </p>
            </div>

            {/* Stats */}
            <div
              className="grid grid-cols-3 gap-8 max-w-2xl mx-auto mb-12 animate-slide-up"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">10K+</div>
                <div className="text-sm text-gray-600">Happy Members</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">95%</div>
                <div className="text-sm text-gray-600">Success Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">24/7</div>
                <div className="text-sm text-gray-600">Support</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Choose Your{" "}
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Perfect Plan
              </span>
            </h2>
            <p className="text-base text-gray-600 max-w-2xl mx-auto">
              Start your transformation today with our flexible pricing options
              designed to fit your lifestyle and budget.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 justify-center items-stretch gap-8 mx-auto">
            {plans.map((plan, index) => (
              <div
                key={plan.id}
                className="flex-1 max-w-md mx-auto"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <PricingCard plan={plan} onSelectPlan={handleSelectPlan} />
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
