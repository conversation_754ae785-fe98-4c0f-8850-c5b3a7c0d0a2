{"name": "razorpay-next", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.365.0", "next": "^15.3.3", "razorpay": "^2.9.2", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "^15.3.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}