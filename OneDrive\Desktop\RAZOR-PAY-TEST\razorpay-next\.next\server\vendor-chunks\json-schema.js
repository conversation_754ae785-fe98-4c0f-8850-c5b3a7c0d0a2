/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json-schema";
exports.ids = ["vendor-chunks/json-schema"];
exports.modules = {

/***/ "(rsc)/./node_modules/json-schema/lib/validate.js":
/*!**************************************************!*\
  !*** ./node_modules/json-schema/lib/validate.js ***!
  \**************************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/**\r\n * JSONSchema Validator - Validates JavaScript objects using JSON Schemas\r\n *\t(http://www.json.com/json-schema-proposal/)\r\n * Licensed under AFL-2.1 OR BSD-3-Clause\r\nTo use the validator call the validate function with an instance object and an optional schema object.\r\nIf a schema is provided, it will be used to validate. If the instance object refers to a schema (self-validating),\r\nthat schema will be used to validate and the schema parameter is not necessary (if both exist,\r\nboth validations will occur).\r\nThe validate method will return an array of validation errors. If there are no errors, then an\r\nempty list will be returned. A validation error will have two properties:\r\n\"property\" which indicates which property had the error\r\n\"message\" which indicates what the error was\r\n */\r\n(function (root, factory) {\r\n    if (true) {\r\n        // AMD. Register as an anonymous module.\r\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function () {\r\n            return factory();\r\n        }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n    } else {}\r\n}(this, function () {// setup primitive classes to be JSON Schema types\r\nvar exports = validate\r\nexports.Integer = {type:\"integer\"};\r\nvar primitiveConstructors = {\r\n\tString: String,\r\n\tBoolean: Boolean,\r\n\tNumber: Number,\r\n\tObject: Object,\r\n\tArray: Array,\r\n\tDate: Date\r\n}\r\nexports.validate = validate;\r\nfunction validate(/*Any*/instance,/*Object*/schema) {\r\n\t\t// Summary:\r\n\t\t//  \tTo use the validator call JSONSchema.validate with an instance object and an optional schema object.\r\n\t\t// \t\tIf a schema is provided, it will be used to validate. If the instance object refers to a schema (self-validating),\r\n\t\t// \t\tthat schema will be used to validate and the schema parameter is not necessary (if both exist,\r\n\t\t// \t\tboth validations will occur).\r\n\t\t// \t\tThe validate method will return an object with two properties:\r\n\t\t// \t\t\tvalid: A boolean indicating if the instance is valid by the schema\r\n\t\t// \t\t\terrors: An array of validation errors. If there are no errors, then an\r\n\t\t// \t\t\t\t\tempty list will be returned. A validation error will have two properties:\r\n\t\t// \t\t\t\t\t\tproperty: which indicates which property had the error\r\n\t\t// \t\t\t\t\t\tmessage: which indicates what the error was\r\n\t\t//\r\n\t\treturn validate(instance, schema, {changing: false});//, coerce: false, existingOnly: false});\r\n\t};\r\nexports.checkPropertyChange = function(/*Any*/value,/*Object*/schema, /*String*/property) {\r\n\t\t// Summary:\r\n\t\t// \t\tThe checkPropertyChange method will check to see if an value can legally be in property with the given schema\r\n\t\t// \t\tThis is slightly different than the validate method in that it will fail if the schema is readonly and it will\r\n\t\t// \t\tnot check for self-validation, it is assumed that the passed in value is already internally valid.\r\n\t\t// \t\tThe checkPropertyChange method will return the same object type as validate, see JSONSchema.validate for\r\n\t\t// \t\tinformation.\r\n\t\t//\r\n\t\treturn validate(value, schema, {changing: property || \"property\"});\r\n\t};\r\nvar validate = exports._validate = function(/*Any*/instance,/*Object*/schema,/*Object*/options) {\r\n\r\n\tif (!options) options = {};\r\n\tvar _changing = options.changing;\r\n\r\n\tfunction getType(schema){\r\n\t\treturn schema.type || (primitiveConstructors[schema.name] == schema && schema.name.toLowerCase());\r\n\t}\r\n\tvar errors = [];\r\n\t// validate a value against a property definition\r\n\tfunction checkProp(value, schema, path,i){\r\n\r\n\t\tvar l;\r\n\t\tpath += path ? typeof i == 'number' ? '[' + i + ']' : typeof i == 'undefined' ? '' : '.' + i : i;\r\n\t\tfunction addError(message){\r\n\t\t\terrors.push({property:path,message:message});\r\n\t\t}\r\n\r\n\t\tif((typeof schema != 'object' || schema instanceof Array) && (path || typeof schema != 'function') && !(schema && getType(schema))){\r\n\t\t\tif(typeof schema == 'function'){\r\n\t\t\t\tif(!(value instanceof schema)){\r\n\t\t\t\t\taddError(\"is not an instance of the class/constructor \" + schema.name);\r\n\t\t\t\t}\r\n\t\t\t}else if(schema){\r\n\t\t\t\taddError(\"Invalid schema/property definition \" + schema);\r\n\t\t\t}\r\n\t\t\treturn null;\r\n\t\t}\r\n\t\tif(_changing && schema.readonly){\r\n\t\t\taddError(\"is a readonly field, it can not be changed\");\r\n\t\t}\r\n\t\tif(schema['extends']){ // if it extends another schema, it must pass that schema as well\r\n\t\t\tcheckProp(value,schema['extends'],path,i);\r\n\t\t}\r\n\t\t// validate a value against a type definition\r\n\t\tfunction checkType(type,value){\r\n\t\t\tif(type){\r\n\t\t\t\tif(typeof type == 'string' && type != 'any' &&\r\n\t\t\t\t\t\t(type == 'null' ? value !== null : typeof value != type) &&\r\n\t\t\t\t\t\t!(value instanceof Array && type == 'array') &&\r\n\t\t\t\t\t\t!(value instanceof Date && type == 'date') &&\r\n\t\t\t\t\t\t!(type == 'integer' && value%1===0)){\r\n\t\t\t\t\treturn [{property:path,message:value + \" - \" + (typeof value) + \" value found, but a \" + type + \" is required\"}];\r\n\t\t\t\t}\r\n\t\t\t\tif(type instanceof Array){\r\n\t\t\t\t\tvar unionErrors=[];\r\n\t\t\t\t\tfor(var j = 0; j < type.length; j++){ // a union type\r\n\t\t\t\t\t\tif(!(unionErrors=checkType(type[j],value)).length){\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(unionErrors.length){\r\n\t\t\t\t\t\treturn unionErrors;\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(typeof type == 'object'){\r\n\t\t\t\t\tvar priorErrors = errors;\r\n\t\t\t\t\terrors = [];\r\n\t\t\t\t\tcheckProp(value,type,path);\r\n\t\t\t\t\tvar theseErrors = errors;\r\n\t\t\t\t\terrors = priorErrors;\r\n\t\t\t\t\treturn theseErrors;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn [];\r\n\t\t}\r\n\t\tif(value === undefined){\r\n\t\t\tif(schema.required){\r\n\t\t\t\taddError(\"is missing and it is required\");\r\n\t\t\t}\r\n\t\t}else{\r\n\t\t\terrors = errors.concat(checkType(getType(schema),value));\r\n\t\t\tif(schema.disallow && !checkType(schema.disallow,value).length){\r\n\t\t\t\taddError(\" disallowed value was matched\");\r\n\t\t\t}\r\n\t\t\tif(value !== null){\r\n\t\t\t\tif(value instanceof Array){\r\n\t\t\t\t\tif(schema.items){\r\n\t\t\t\t\t\tvar itemsIsArray = schema.items instanceof Array;\r\n\t\t\t\t\t\tvar propDef = schema.items;\r\n\t\t\t\t\t\tfor (i = 0, l = value.length; i < l; i += 1) {\r\n\t\t\t\t\t\t\tif (itemsIsArray)\r\n\t\t\t\t\t\t\t\tpropDef = schema.items[i];\r\n\t\t\t\t\t\t\tif (options.coerce)\r\n\t\t\t\t\t\t\t\tvalue[i] = options.coerce(value[i], propDef);\r\n\t\t\t\t\t\t\terrors.concat(checkProp(value[i],propDef,path,i));\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(schema.minItems && value.length < schema.minItems){\r\n\t\t\t\t\t\taddError(\"There must be a minimum of \" + schema.minItems + \" in the array\");\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(schema.maxItems && value.length > schema.maxItems){\r\n\t\t\t\t\t\taddError(\"There must be a maximum of \" + schema.maxItems + \" in the array\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}else if(schema.properties || schema.additionalProperties){\r\n\t\t\t\t\terrors.concat(checkObj(value, schema.properties, path, schema.additionalProperties));\r\n\t\t\t\t}\r\n\t\t\t\tif(schema.pattern && typeof value == 'string' && !value.match(schema.pattern)){\r\n\t\t\t\t\taddError(\"does not match the regex pattern \" + schema.pattern);\r\n\t\t\t\t}\r\n\t\t\t\tif(schema.maxLength && typeof value == 'string' && value.length > schema.maxLength){\r\n\t\t\t\t\taddError(\"may only be \" + schema.maxLength + \" characters long\");\r\n\t\t\t\t}\r\n\t\t\t\tif(schema.minLength && typeof value == 'string' && value.length < schema.minLength){\r\n\t\t\t\t\taddError(\"must be at least \" + schema.minLength + \" characters long\");\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof schema.minimum !== 'undefined' && typeof value == typeof schema.minimum &&\r\n\t\t\t\t\t\tschema.minimum > value){\r\n\t\t\t\t\taddError(\"must have a minimum value of \" + schema.minimum);\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof schema.maximum !== 'undefined' && typeof value == typeof schema.maximum &&\r\n\t\t\t\t\t\tschema.maximum < value){\r\n\t\t\t\t\taddError(\"must have a maximum value of \" + schema.maximum);\r\n\t\t\t\t}\r\n\t\t\t\tif(schema['enum']){\r\n\t\t\t\t\tvar enumer = schema['enum'];\r\n\t\t\t\t\tl = enumer.length;\r\n\t\t\t\t\tvar found;\r\n\t\t\t\t\tfor(var j = 0; j < l; j++){\r\n\t\t\t\t\t\tif(enumer[j]===value){\r\n\t\t\t\t\t\t\tfound=1;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!found){\r\n\t\t\t\t\t\taddError(\"does not have a value in the enumeration \" + enumer.join(\", \"));\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(typeof schema.maxDecimal == 'number' &&\r\n\t\t\t\t\t(value.toString().match(new RegExp(\"\\\\.[0-9]{\" + (schema.maxDecimal + 1) + \",}\")))){\r\n\t\t\t\t\taddError(\"may only have \" + schema.maxDecimal + \" digits of decimal places\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn null;\r\n\t}\r\n\t// validate an object against a schema\r\n\tfunction checkObj(instance,objTypeDef,path,additionalProp){\r\n\r\n\t\tif(typeof objTypeDef =='object'){\r\n\t\t\tif(typeof instance != 'object' || instance instanceof Array){\r\n\t\t\t\terrors.push({property:path,message:\"an object is required\"});\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tfor(var i in objTypeDef){ \r\n\t\t\t\tif(objTypeDef.hasOwnProperty(i) && i != '__proto__' && i != 'constructor'){\r\n\t\t\t\t\tvar value = instance.hasOwnProperty(i) ? instance[i] : undefined;\r\n\t\t\t\t\t// skip _not_ specified properties\r\n\t\t\t\t\tif (value === undefined && options.existingOnly) continue;\r\n\t\t\t\t\tvar propDef = objTypeDef[i];\r\n\t\t\t\t\t// set default\r\n\t\t\t\t\tif(value === undefined && propDef[\"default\"]){\r\n\t\t\t\t\t\tvalue = instance[i] = propDef[\"default\"];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(options.coerce && i in instance){\r\n\t\t\t\t\t\tvalue = instance[i] = options.coerce(value, propDef);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcheckProp(value,propDef,path,i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor(i in instance){\r\n\t\t\tif(instance.hasOwnProperty(i) && !(i.charAt(0) == '_' && i.charAt(1) == '_') && objTypeDef && !objTypeDef[i] && additionalProp===false){\r\n\t\t\t\tif (options.filter) {\r\n\t\t\t\t\tdelete instance[i];\r\n\t\t\t\t\tcontinue;\r\n\t\t\t\t} else {\r\n\t\t\t\t\terrors.push({property:path,message:\"The property \" + i +\r\n\t\t\t\t\t\t\" is not defined in the schema and the schema does not allow additional properties\"});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tvar requires = objTypeDef && objTypeDef[i] && objTypeDef[i].requires;\r\n\t\t\tif(requires && !(requires in instance)){\r\n\t\t\t\terrors.push({property:path,message:\"the presence of the property \" + i + \" requires that \" + requires + \" also be present\"});\r\n\t\t\t}\r\n\t\t\tvalue = instance[i];\r\n\t\t\tif(additionalProp && (!(objTypeDef && typeof objTypeDef == 'object') || !(i in objTypeDef))){\r\n\t\t\t\tif(options.coerce){\r\n\t\t\t\t\tvalue = instance[i] = options.coerce(value, additionalProp);\r\n\t\t\t\t}\r\n\t\t\t\tcheckProp(value,additionalProp,path,i);\r\n\t\t\t}\r\n\t\t\tif(!_changing && value && value.$schema){\r\n\t\t\t\terrors = errors.concat(checkProp(value,value.$schema,path,i));\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn errors;\r\n\t}\r\n\tif(schema){\r\n\t\tcheckProp(instance,schema,'',_changing || '');\r\n\t}\r\n\tif(!_changing && instance && instance.$schema){\r\n\t\tcheckProp(instance,instance.$schema,'','');\r\n\t}\r\n\treturn {valid:!errors.length,errors:errors};\r\n};\r\nexports.mustBeValid = function(result){\r\n\t//\tsummary:\r\n\t//\t\tThis checks to ensure that the result is valid and will throw an appropriate error message if it is not\r\n\t// result: the result returned from checkPropertyChange or validate\r\n\tif(!result.valid){\r\n\t\tthrow new TypeError(result.errors.map(function(error){return \"for property \" + error.property + ': ' + error.message;}).join(\", \\n\"));\r\n\t}\r\n}\r\n\r\nreturn exports;\r\n}));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/json-schema/lib/validate.js\n");

/***/ })

};
;