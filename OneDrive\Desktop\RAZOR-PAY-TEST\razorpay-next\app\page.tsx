"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle2, LoaderCircle, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { apiClient, authStorage, Plan } from "@/lib/api";

type PricingCardProps = {
  plan: Plan;
  onSelectPlan: (planId: string, durationId: string, price: number) => void;
};

const PricingCard = ({ plan, onSelectPlan }: PricingCardProps) => (
  <Card className="max-w-80 space-y-6">
    <CardHeader className="pb-8 pt-4 gap-8">
      <CardTitle>{plan.name}</CardTitle>
    </CardHeader>
    <CardContent className="flex flex-col gap-4">
      <CardDescription className="pt-1.5 h-12">
        {plan.description}
      </CardDescription>

      {plan.durations.map((duration) => (
        <div key={duration.id} className="border rounded-lg p-4 space-y-3">
          <div className="flex justify-between items-center">
            <h4 className="font-semibold">{duration.label}</h4>
            <span className="text-2xl font-bold">₹{duration.price}</span>
          </div>

          <div className="space-y-2">
            {duration.features.map((feature) => (
              <span
                key={feature.id}
                className="flex items-center gap-2 text-sm text-muted-foreground"
              >
                <CheckCircle2 className="text-green-500 h-4 w-4" />
                {feature.name}
              </span>
            ))}
          </div>

          <Button
            className="w-full"
            onClick={() => onSelectPlan(plan.id, duration.id, duration.price)}
          >
            Select {duration.label}
          </Button>
        </div>
      ))}
    </CardContent>
  </Card>
);

export default function HomePage() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const auth = authStorage.getAuth();
    if (!auth || !authStorage.isAuthenticated()) {
      router.push("/login");
      return;
    }
    setUser(auth);
    fetchPlans(auth.accessToken);
  }, [router]);

  const fetchPlans = async (token: string) => {
    try {
      setLoading(true);
      const plansData = await apiClient.getPlans(token);
      setPlans(plansData);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch plans"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPlan = (
    planId: string,
    durationId: string,
    price: number
  ) => {
    router.push(
      `/checkout?planId=${planId}&durationId=${durationId}&price=${price}`
    );
  };

  const handleLogout = () => {
    authStorage.clearAuth();
    router.push("/login");
  };

  if (loading) {
    return (
      <div className="container h-screen flex justify-center items-center">
        <LoaderCircle className="animate-spin h-20 w-20 text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container h-screen flex flex-col justify-center items-center gap-4">
        <p className="text-red-500">{error}</p>
        <Button onClick={() => fetchPlans(user?.accessToken)}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="container py-8 flex flex-col items-center justify-center text-center">
      <div className="w-full flex justify-between items-center mb-8">
        <div>
          <h1 className="scroll-m-20 text-4xl font-extrabold tracking-tight">
            Pricing Plans
          </h1>
          <p className="text-muted-foreground">Welcome, {user?.firstName}!</p>
        </div>
        <Button variant="outline" onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </Button>
      </div>

      <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight mb-8">
        Choose the plan that's right for you
      </h2>

      <section className="flex flex-col sm:flex-row sm:flex-wrap justify-center gap-8">
        {plans.map((plan) => (
          <PricingCard
            key={plan.id}
            plan={plan}
            onSelectPlan={handleSelectPlan}
          />
        ))}
      </section>
    </div>
  );
}
