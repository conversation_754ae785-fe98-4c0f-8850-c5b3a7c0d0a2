"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle2, LoaderCircle, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { apiClient, authStorage, Plan } from "@/lib/api";

type PricingCardProps = {
  plan: Plan;
  onSelectPlan: (planId: string, durationId: string, price: number) => void;
};

const PricingCard = ({ plan, onSelectPlan }: PricingCardProps) => (
  <Card className="max-w-80 space-y-6">
    <CardHeader className="pb-8 pt-4 gap-8">
      <CardTitle>{plan.name}</CardTitle>
    </CardHeader>
    <CardContent className="flex flex-col gap-4">
      <CardDescription className="pt-1.5 h-12">
        {plan.description}
      </CardDescription>

      {plan.durations.map((duration) => (
        <div key={duration.id} className="border rounded-lg p-4 space-y-3">
          <div className="flex justify-between items-center">
            <h4 className="font-semibold">{duration.label}</h4>
            <span className="text-2xl font-bold">₹{duration.price}</span>
          </div>

          <div className="space-y-2">
            {duration.features.map((feature) => (
              <span
                key={feature.id}
                className="flex items-center gap-2 text-sm text-muted-foreground"
              >
                <CheckCircle2 className="text-green-500 h-4 w-4" />
                {feature.name}
              </span>
            ))}
          </div>

          <Button
            className="w-full"
            onClick={() => onSelectPlan(plan.id, duration.id, duration.price)}
          >
            Select {duration.label}
          </Button>
        </div>
      ))}
    </CardContent>
  </Card>
);

export default function HomePage() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const auth = authStorage.getAuth();
    if (!auth || !authStorage.isAuthenticated()) {
      // For demo purposes, create a mock user if backend is not available
      const mockUser = {
        email: "<EMAIL>",
        firstName: "Demo",
        lastName: "User",
        accessToken: "mock-token",
        accessTokenExp: Date.now() / 1000 + 3600, // 1 hour from now
      };
      setUser(mockUser);
      fetchPlans(mockUser.accessToken);
      return;
    }
    setUser(auth);
    fetchPlans(auth.accessToken);
  }, [router]);

  const fetchPlans = async (token: string) => {
    try {
      setLoading(true);
      // Try to fetch from API first, fallback to mock data if backend is not running
      try {
        const plansData = await apiClient.getPlans(token);
        setPlans(plansData);
      } catch (apiError) {
        console.log("Backend not available, using mock data for demo");
        // Mock data based on your API response structure
        const mockPlans: Plan[] = [
          {
            id: "68372a398cd0aff33c968ed9",
            name: "Workout Plan",
            slug: "workout-plan",
            description:
              "Custom workout programming designed for strength, endurance, and mobility.",
            durations: [
              {
                id: "6837281c8cd0aff33c968ec3",
                label: "3 Months",
                valueInDays: 90,
                slug: "3-months",
                features: [
                  {
                    id: "683725788cd0aff33c968e80",
                    name: "Diet Consultation",
                    description:
                      "Personalized diet consulting tailored to user goals.",
                    slug: "diet-consultation",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:02:16.489Z",
                    updatedAt: "2025-05-28T15:02:16.489Z",
                  },
                  {
                    id: "683725cd8cd0aff33c968e94",
                    name: "Diet/Workout Plan Update",
                    description:
                      "Revisions to existing plans based on user progress.",
                    slug: "diet/workout-plan-update",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:03:41.613Z",
                    updatedAt: "2025-05-28T15:03:41.613Z",
                  },
                  {
                    id: "683725df8cd0aff33c968e98",
                    name: "Wellness Trackers",
                    description:
                      "Daily tracking of steps, sleep, and water intake.",
                    slug: "wellness-trackers",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:03:59.903Z",
                    updatedAt: "2025-05-28T15:03:59.903Z",
                  },
                  {
                    id: "683725ec8cd0aff33c968e9c",
                    name: "Monthly Check-in Calls",
                    description:
                      "4–6 calls/month (minimum 3 minutes each) based on plan.",
                    slug: "monthly-check-in-calls",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:04:12.424Z",
                    updatedAt: "2025-05-28T15:04:12.424Z",
                  },
                ],
                price: 3500,
                currency: "INR",
                paymentType: "one-time",
                isActive: true,
                customSort: 0,
                createdAt: "2025-05-28T15:13:32.472Z",
                updatedAt: "2025-06-02T18:18:35.831Z",
              },
              {
                id: "683728358cd0aff33c968ec7",
                label: "6 Months",
                valueInDays: 180,
                slug: "6-months",
                features: [
                  {
                    id: "683725788cd0aff33c968e80",
                    name: "Diet Consultation",
                    description:
                      "Personalized diet consulting tailored to user goals.",
                    slug: "diet-consultation",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:02:16.489Z",
                    updatedAt: "2025-05-28T15:02:16.489Z",
                  },
                  {
                    id: "683725838cd0aff33c968e84",
                    name: "Workout Consultation",
                    description:
                      "One-on-one workout consultation, goal-specific planning.",
                    slug: "workout-consultation",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:02:27.592Z",
                    updatedAt: "2025-05-28T15:02:27.592Z",
                  },
                  {
                    id: "683725af8cd0aff33c968e8c",
                    name: "Basics of Supplementation",
                    description:
                      "Educational module on supplement usage and essentials.",
                    slug: "basics-of-supplementation",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:03:11.296Z",
                    updatedAt: "2025-05-28T15:03:11.296Z",
                  },
                  {
                    id: "683725fd8cd0aff33c968ea0",
                    name: "Chat Support",
                    description:
                      "Ongoing chat support for quick queries and guidance.",
                    slug: "chat-support",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:04:29.826Z",
                    updatedAt: "2025-05-28T15:04:29.826Z",
                  },
                ],
                price: 7500,
                currency: "INR",
                paymentType: "one-time",
                isActive: true,
                customSort: 0,
                createdAt: "2025-05-28T15:13:57.897Z",
                updatedAt: "2025-06-02T02:53:21.461Z",
              },
              {
                id: "683728538cd0aff33c968ecb",
                label: "1 Year",
                valueInDays: 365,
                slug: "1-year",
                features: [
                  {
                    id: "683725788cd0aff33c968e80",
                    name: "Diet Consultation",
                    description:
                      "Personalized diet consulting tailored to user goals.",
                    slug: "diet-consultation",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:02:16.489Z",
                    updatedAt: "2025-05-28T15:02:16.489Z",
                  },
                  {
                    id: "683725838cd0aff33c968e84",
                    name: "Workout Consultation",
                    description:
                      "One-on-one workout consultation, goal-specific planning.",
                    slug: "workout-consultation",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:02:27.592Z",
                    updatedAt: "2025-05-28T15:02:27.592Z",
                  },
                  {
                    id: "683725fd8cd0aff33c968ea0",
                    name: "Chat Support",
                    description:
                      "Ongoing chat support for quick queries and guidance.",
                    slug: "chat-support",
                    isActive: true,
                    customSort: 0,
                    createdAt: "2025-05-28T15:04:29.826Z",
                    updatedAt: "2025-05-28T15:04:29.826Z",
                  },
                  {
                    id: "683726138cd0aff33c968ea4",
                    name: "Custom Education Request",
                    description:
                      "On-demand education modules on topics of your choice.",
                    slug: "custom-education-request",
                    isActive: true,
                    customSort: 1,
                    createdAt: "2025-05-28T15:04:51.241Z",
                    updatedAt: "2025-06-02T02:36:42.526Z",
                  },
                ],
                price: 52000,
                currency: "INR",
                paymentType: "one-time",
                isActive: true,
                customSort: 0,
                createdAt: "2025-06-02T03:44:40.933Z",
                updatedAt: "2025-06-02T03:44:40.933Z",
              },
            ],
            isActive: true,
            isPopular: false,
            customSort: 0,
            createdAt: "2025-05-28T15:22:33.284Z",
            updatedAt: "2025-06-02T03:44:51.200Z",
          },
        ];
        setPlans(mockPlans);
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch plans"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPlan = (
    planId: string,
    durationId: string,
    price: number
  ) => {
    router.push(
      `/checkout?planId=${planId}&durationId=${durationId}&price=${price}`
    );
  };

  const handleLogout = () => {
    authStorage.clearAuth();
    router.push("/login");
  };

  if (loading) {
    return (
      <div className="container h-screen flex justify-center items-center">
        <LoaderCircle className="animate-spin h-20 w-20 text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container h-screen flex flex-col justify-center items-center gap-4">
        <p className="text-red-500">{error}</p>
        <Button onClick={() => fetchPlans(user?.accessToken)}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="container py-8 flex flex-col items-center justify-center text-center">
      <div className="w-full flex justify-between items-center mb-8">
        <div>
          <h1 className="scroll-m-20 text-4xl font-extrabold tracking-tight">
            Pricing Plans
          </h1>
          <p className="text-muted-foreground">Welcome, {user?.firstName}!</p>
        </div>
        <Button variant="outline" onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </Button>
      </div>

      <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight mb-8">
        Choose the plan that&apos;s right for you
      </h2>

      <section className="flex flex-col sm:flex-row sm:flex-wrap justify-center gap-8">
        {plans.map((plan) => (
          <PricingCard
            key={plan.id}
            plan={plan}
            onSelectPlan={handleSelectPlan}
          />
        ))}
      </section>
    </div>
  );
}
