/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tough-cookie";
exports.ids = ["vendor-chunks/tough-cookie"];
exports.modules = {

/***/ "(rsc)/./node_modules/tough-cookie/lib/cookie.js":
/*!*************************************************!*\
  !*** ./node_modules/tough-cookie/lib/cookie.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar net = __webpack_require__(/*! net */ \"net\");\nvar urlParse = (__webpack_require__(/*! url */ \"url\").parse);\nvar util = __webpack_require__(/*! util */ \"util\");\nvar pubsuffix = __webpack_require__(/*! ./pubsuffix-psl */ \"(rsc)/./node_modules/tough-cookie/lib/pubsuffix-psl.js\");\nvar Store = (__webpack_require__(/*! ./store */ \"(rsc)/./node_modules/tough-cookie/lib/store.js\").Store);\nvar MemoryCookieStore = (__webpack_require__(/*! ./memstore */ \"(rsc)/./node_modules/tough-cookie/lib/memstore.js\").MemoryCookieStore);\nvar pathMatch = (__webpack_require__(/*! ./pathMatch */ \"(rsc)/./node_modules/tough-cookie/lib/pathMatch.js\").pathMatch);\nvar VERSION = __webpack_require__(/*! ./version */ \"(rsc)/./node_modules/tough-cookie/lib/version.js\");\n\nvar punycode;\ntry {\n  punycode = __webpack_require__(/*! punycode */ \"punycode\");\n} catch(e) {\n  console.warn(\"tough-cookie: can't load punycode; won't use punycode for domain normalization\");\n}\n\n// From RFC6265 S4.1.1\n// note that it excludes \\x3B \";\"\nvar COOKIE_OCTETS = /^[\\x21\\x23-\\x2B\\x2D-\\x3A\\x3C-\\x5B\\x5D-\\x7E]+$/;\n\nvar CONTROL_CHARS = /[\\x00-\\x1F]/;\n\n// From Chromium // '\\r', '\\n' and '\\0' should be treated as a terminator in\n// the \"relaxed\" mode, see:\n// https://github.com/ChromiumWebApps/chromium/blob/b3d3b4da8bb94c1b2e061600df106d590fda3620/net/cookies/parsed_cookie.cc#L60\nvar TERMINATORS = ['\\n', '\\r', '\\0'];\n\n// RFC6265 S4.1.1 defines path value as 'any CHAR except CTLs or \";\"'\n// Note ';' is \\x3B\nvar PATH_VALUE = /[\\x20-\\x3A\\x3C-\\x7E]+/;\n\n// date-time parsing constants (RFC6265 S5.1.1)\n\nvar DATE_DELIM = /[\\x09\\x20-\\x2F\\x3B-\\x40\\x5B-\\x60\\x7B-\\x7E]/;\n\nvar MONTH_TO_NUM = {\n  jan:0, feb:1, mar:2, apr:3, may:4, jun:5,\n  jul:6, aug:7, sep:8, oct:9, nov:10, dec:11\n};\nvar NUM_TO_MONTH = [\n  'Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'\n];\nvar NUM_TO_DAY = [\n  'Sun','Mon','Tue','Wed','Thu','Fri','Sat'\n];\n\nvar MAX_TIME = 2147483647000; // 31-bit max\nvar MIN_TIME = 0; // 31-bit min\n\n/*\n * Parses a Natural number (i.e., non-negative integer) with either the\n *    <min>*<max>DIGIT ( non-digit *OCTET )\n * or\n *    <min>*<max>DIGIT\n * grammar (RFC6265 S5.1.1).\n *\n * The \"trailingOK\" boolean controls if the grammar accepts a\n * \"( non-digit *OCTET )\" trailer.\n */\nfunction parseDigits(token, minDigits, maxDigits, trailingOK) {\n  var count = 0;\n  while (count < token.length) {\n    var c = token.charCodeAt(count);\n    // \"non-digit = %x00-2F / %x3A-FF\"\n    if (c <= 0x2F || c >= 0x3A) {\n      break;\n    }\n    count++;\n  }\n\n  // constrain to a minimum and maximum number of digits.\n  if (count < minDigits || count > maxDigits) {\n    return null;\n  }\n\n  if (!trailingOK && count != token.length) {\n    return null;\n  }\n\n  return parseInt(token.substr(0,count), 10);\n}\n\nfunction parseTime(token) {\n  var parts = token.split(':');\n  var result = [0,0,0];\n\n  /* RF6256 S5.1.1:\n   *      time            = hms-time ( non-digit *OCTET )\n   *      hms-time        = time-field \":\" time-field \":\" time-field\n   *      time-field      = 1*2DIGIT\n   */\n\n  if (parts.length !== 3) {\n    return null;\n  }\n\n  for (var i = 0; i < 3; i++) {\n    // \"time-field\" must be strictly \"1*2DIGIT\", HOWEVER, \"hms-time\" can be\n    // followed by \"( non-digit *OCTET )\" so therefore the last time-field can\n    // have a trailer\n    var trailingOK = (i == 2);\n    var num = parseDigits(parts[i], 1, 2, trailingOK);\n    if (num === null) {\n      return null;\n    }\n    result[i] = num;\n  }\n\n  return result;\n}\n\nfunction parseMonth(token) {\n  token = String(token).substr(0,3).toLowerCase();\n  var num = MONTH_TO_NUM[token];\n  return num >= 0 ? num : null;\n}\n\n/*\n * RFC6265 S5.1.1 date parser (see RFC for full grammar)\n */\nfunction parseDate(str) {\n  if (!str) {\n    return;\n  }\n\n  /* RFC6265 S5.1.1:\n   * 2. Process each date-token sequentially in the order the date-tokens\n   * appear in the cookie-date\n   */\n  var tokens = str.split(DATE_DELIM);\n  if (!tokens) {\n    return;\n  }\n\n  var hour = null;\n  var minute = null;\n  var second = null;\n  var dayOfMonth = null;\n  var month = null;\n  var year = null;\n\n  for (var i=0; i<tokens.length; i++) {\n    var token = tokens[i].trim();\n    if (!token.length) {\n      continue;\n    }\n\n    var result;\n\n    /* 2.1. If the found-time flag is not set and the token matches the time\n     * production, set the found-time flag and set the hour- value,\n     * minute-value, and second-value to the numbers denoted by the digits in\n     * the date-token, respectively.  Skip the remaining sub-steps and continue\n     * to the next date-token.\n     */\n    if (second === null) {\n      result = parseTime(token);\n      if (result) {\n        hour = result[0];\n        minute = result[1];\n        second = result[2];\n        continue;\n      }\n    }\n\n    /* 2.2. If the found-day-of-month flag is not set and the date-token matches\n     * the day-of-month production, set the found-day-of- month flag and set\n     * the day-of-month-value to the number denoted by the date-token.  Skip\n     * the remaining sub-steps and continue to the next date-token.\n     */\n    if (dayOfMonth === null) {\n      // \"day-of-month = 1*2DIGIT ( non-digit *OCTET )\"\n      result = parseDigits(token, 1, 2, true);\n      if (result !== null) {\n        dayOfMonth = result;\n        continue;\n      }\n    }\n\n    /* 2.3. If the found-month flag is not set and the date-token matches the\n     * month production, set the found-month flag and set the month-value to\n     * the month denoted by the date-token.  Skip the remaining sub-steps and\n     * continue to the next date-token.\n     */\n    if (month === null) {\n      result = parseMonth(token);\n      if (result !== null) {\n        month = result;\n        continue;\n      }\n    }\n\n    /* 2.4. If the found-year flag is not set and the date-token matches the\n     * year production, set the found-year flag and set the year-value to the\n     * number denoted by the date-token.  Skip the remaining sub-steps and\n     * continue to the next date-token.\n     */\n    if (year === null) {\n      // \"year = 2*4DIGIT ( non-digit *OCTET )\"\n      result = parseDigits(token, 2, 4, true);\n      if (result !== null) {\n        year = result;\n        /* From S5.1.1:\n         * 3.  If the year-value is greater than or equal to 70 and less\n         * than or equal to 99, increment the year-value by 1900.\n         * 4.  If the year-value is greater than or equal to 0 and less\n         * than or equal to 69, increment the year-value by 2000.\n         */\n        if (year >= 70 && year <= 99) {\n          year += 1900;\n        } else if (year >= 0 && year <= 69) {\n          year += 2000;\n        }\n      }\n    }\n  }\n\n  /* RFC 6265 S5.1.1\n   * \"5. Abort these steps and fail to parse the cookie-date if:\n   *     *  at least one of the found-day-of-month, found-month, found-\n   *        year, or found-time flags is not set,\n   *     *  the day-of-month-value is less than 1 or greater than 31,\n   *     *  the year-value is less than 1601,\n   *     *  the hour-value is greater than 23,\n   *     *  the minute-value is greater than 59, or\n   *     *  the second-value is greater than 59.\n   *     (Note that leap seconds cannot be represented in this syntax.)\"\n   *\n   * So, in order as above:\n   */\n  if (\n    dayOfMonth === null || month === null || year === null || second === null ||\n    dayOfMonth < 1 || dayOfMonth > 31 ||\n    year < 1601 ||\n    hour > 23 ||\n    minute > 59 ||\n    second > 59\n  ) {\n    return;\n  }\n\n  return new Date(Date.UTC(year, month, dayOfMonth, hour, minute, second));\n}\n\nfunction formatDate(date) {\n  var d = date.getUTCDate(); d = d >= 10 ? d : '0'+d;\n  var h = date.getUTCHours(); h = h >= 10 ? h : '0'+h;\n  var m = date.getUTCMinutes(); m = m >= 10 ? m : '0'+m;\n  var s = date.getUTCSeconds(); s = s >= 10 ? s : '0'+s;\n  return NUM_TO_DAY[date.getUTCDay()] + ', ' +\n    d+' '+ NUM_TO_MONTH[date.getUTCMonth()] +' '+ date.getUTCFullYear() +' '+\n    h+':'+m+':'+s+' GMT';\n}\n\n// S5.1.2 Canonicalized Host Names\nfunction canonicalDomain(str) {\n  if (str == null) {\n    return null;\n  }\n  str = str.trim().replace(/^\\./,''); // S4.1.2.3 & S5.2.3: ignore leading .\n\n  // convert to IDN if any non-ASCII characters\n  if (punycode && /[^\\u0001-\\u007f]/.test(str)) {\n    str = punycode.toASCII(str);\n  }\n\n  return str.toLowerCase();\n}\n\n// S5.1.3 Domain Matching\nfunction domainMatch(str, domStr, canonicalize) {\n  if (str == null || domStr == null) {\n    return null;\n  }\n  if (canonicalize !== false) {\n    str = canonicalDomain(str);\n    domStr = canonicalDomain(domStr);\n  }\n\n  /*\n   * \"The domain string and the string are identical. (Note that both the\n   * domain string and the string will have been canonicalized to lower case at\n   * this point)\"\n   */\n  if (str == domStr) {\n    return true;\n  }\n\n  /* \"All of the following [three] conditions hold:\" (order adjusted from the RFC) */\n\n  /* \"* The string is a host name (i.e., not an IP address).\" */\n  if (net.isIP(str)) {\n    return false;\n  }\n\n  /* \"* The domain string is a suffix of the string\" */\n  var idx = str.indexOf(domStr);\n  if (idx <= 0) {\n    return false; // it's a non-match (-1) or prefix (0)\n  }\n\n  // e.g \"a.b.c\".indexOf(\"b.c\") === 2\n  // 5 === 3+2\n  if (str.length !== domStr.length + idx) { // it's not a suffix\n    return false;\n  }\n\n  /* \"* The last character of the string that is not included in the domain\n  * string is a %x2E (\".\") character.\" */\n  if (str.substr(idx-1,1) !== '.') {\n    return false;\n  }\n\n  return true;\n}\n\n\n// RFC6265 S5.1.4 Paths and Path-Match\n\n/*\n * \"The user agent MUST use an algorithm equivalent to the following algorithm\n * to compute the default-path of a cookie:\"\n *\n * Assumption: the path (and not query part or absolute uri) is passed in.\n */\nfunction defaultPath(path) {\n  // \"2. If the uri-path is empty or if the first character of the uri-path is not\n  // a %x2F (\"/\") character, output %x2F (\"/\") and skip the remaining steps.\n  if (!path || path.substr(0,1) !== \"/\") {\n    return \"/\";\n  }\n\n  // \"3. If the uri-path contains no more than one %x2F (\"/\") character, output\n  // %x2F (\"/\") and skip the remaining step.\"\n  if (path === \"/\") {\n    return path;\n  }\n\n  var rightSlash = path.lastIndexOf(\"/\");\n  if (rightSlash === 0) {\n    return \"/\";\n  }\n\n  // \"4. Output the characters of the uri-path from the first character up to,\n  // but not including, the right-most %x2F (\"/\").\"\n  return path.slice(0, rightSlash);\n}\n\nfunction trimTerminator(str) {\n  for (var t = 0; t < TERMINATORS.length; t++) {\n    var terminatorIdx = str.indexOf(TERMINATORS[t]);\n    if (terminatorIdx !== -1) {\n      str = str.substr(0,terminatorIdx);\n    }\n  }\n\n  return str;\n}\n\nfunction parseCookiePair(cookiePair, looseMode) {\n  cookiePair = trimTerminator(cookiePair);\n\n  var firstEq = cookiePair.indexOf('=');\n  if (looseMode) {\n    if (firstEq === 0) { // '=' is immediately at start\n      cookiePair = cookiePair.substr(1);\n      firstEq = cookiePair.indexOf('='); // might still need to split on '='\n    }\n  } else { // non-loose mode\n    if (firstEq <= 0) { // no '=' or is at start\n      return; // needs to have non-empty \"cookie-name\"\n    }\n  }\n\n  var cookieName, cookieValue;\n  if (firstEq <= 0) {\n    cookieName = \"\";\n    cookieValue = cookiePair.trim();\n  } else {\n    cookieName = cookiePair.substr(0, firstEq).trim();\n    cookieValue = cookiePair.substr(firstEq+1).trim();\n  }\n\n  if (CONTROL_CHARS.test(cookieName) || CONTROL_CHARS.test(cookieValue)) {\n    return;\n  }\n\n  var c = new Cookie();\n  c.key = cookieName;\n  c.value = cookieValue;\n  return c;\n}\n\nfunction parse(str, options) {\n  if (!options || typeof options !== 'object') {\n    options = {};\n  }\n  str = str.trim();\n\n  // We use a regex to parse the \"name-value-pair\" part of S5.2\n  var firstSemi = str.indexOf(';'); // S5.2 step 1\n  var cookiePair = (firstSemi === -1) ? str : str.substr(0, firstSemi);\n  var c = parseCookiePair(cookiePair, !!options.loose);\n  if (!c) {\n    return;\n  }\n\n  if (firstSemi === -1) {\n    return c;\n  }\n\n  // S5.2.3 \"unparsed-attributes consist of the remainder of the set-cookie-string\n  // (including the %x3B (\";\") in question).\" plus later on in the same section\n  // \"discard the first \";\" and trim\".\n  var unparsed = str.slice(firstSemi + 1).trim();\n\n  // \"If the unparsed-attributes string is empty, skip the rest of these\n  // steps.\"\n  if (unparsed.length === 0) {\n    return c;\n  }\n\n  /*\n   * S5.2 says that when looping over the items \"[p]rocess the attribute-name\n   * and attribute-value according to the requirements in the following\n   * subsections\" for every item.  Plus, for many of the individual attributes\n   * in S5.3 it says to use the \"attribute-value of the last attribute in the\n   * cookie-attribute-list\".  Therefore, in this implementation, we overwrite\n   * the previous value.\n   */\n  var cookie_avs = unparsed.split(';');\n  while (cookie_avs.length) {\n    var av = cookie_avs.shift().trim();\n    if (av.length === 0) { // happens if \";;\" appears\n      continue;\n    }\n    var av_sep = av.indexOf('=');\n    var av_key, av_value;\n\n    if (av_sep === -1) {\n      av_key = av;\n      av_value = null;\n    } else {\n      av_key = av.substr(0,av_sep);\n      av_value = av.substr(av_sep+1);\n    }\n\n    av_key = av_key.trim().toLowerCase();\n\n    if (av_value) {\n      av_value = av_value.trim();\n    }\n\n    switch(av_key) {\n    case 'expires': // S5.2.1\n      if (av_value) {\n        var exp = parseDate(av_value);\n        // \"If the attribute-value failed to parse as a cookie date, ignore the\n        // cookie-av.\"\n        if (exp) {\n          // over and underflow not realistically a concern: V8's getTime() seems to\n          // store something larger than a 32-bit time_t (even with 32-bit node)\n          c.expires = exp;\n        }\n      }\n      break;\n\n    case 'max-age': // S5.2.2\n      if (av_value) {\n        // \"If the first character of the attribute-value is not a DIGIT or a \"-\"\n        // character ...[or]... If the remainder of attribute-value contains a\n        // non-DIGIT character, ignore the cookie-av.\"\n        if (/^-?[0-9]+$/.test(av_value)) {\n          var delta = parseInt(av_value, 10);\n          // \"If delta-seconds is less than or equal to zero (0), let expiry-time\n          // be the earliest representable date and time.\"\n          c.setMaxAge(delta);\n        }\n      }\n      break;\n\n    case 'domain': // S5.2.3\n      // \"If the attribute-value is empty, the behavior is undefined.  However,\n      // the user agent SHOULD ignore the cookie-av entirely.\"\n      if (av_value) {\n        // S5.2.3 \"Let cookie-domain be the attribute-value without the leading %x2E\n        // (\".\") character.\"\n        var domain = av_value.trim().replace(/^\\./, '');\n        if (domain) {\n          // \"Convert the cookie-domain to lower case.\"\n          c.domain = domain.toLowerCase();\n        }\n      }\n      break;\n\n    case 'path': // S5.2.4\n      /*\n       * \"If the attribute-value is empty or if the first character of the\n       * attribute-value is not %x2F (\"/\"):\n       *   Let cookie-path be the default-path.\n       * Otherwise:\n       *   Let cookie-path be the attribute-value.\"\n       *\n       * We'll represent the default-path as null since it depends on the\n       * context of the parsing.\n       */\n      c.path = av_value && av_value[0] === \"/\" ? av_value : null;\n      break;\n\n    case 'secure': // S5.2.5\n      /*\n       * \"If the attribute-name case-insensitively matches the string \"Secure\",\n       * the user agent MUST append an attribute to the cookie-attribute-list\n       * with an attribute-name of Secure and an empty attribute-value.\"\n       */\n      c.secure = true;\n      break;\n\n    case 'httponly': // S5.2.6 -- effectively the same as 'secure'\n      c.httpOnly = true;\n      break;\n\n    default:\n      c.extensions = c.extensions || [];\n      c.extensions.push(av);\n      break;\n    }\n  }\n\n  return c;\n}\n\n// avoid the V8 deoptimization monster!\nfunction jsonParse(str) {\n  var obj;\n  try {\n    obj = JSON.parse(str);\n  } catch (e) {\n    return e;\n  }\n  return obj;\n}\n\nfunction fromJSON(str) {\n  if (!str) {\n    return null;\n  }\n\n  var obj;\n  if (typeof str === 'string') {\n    obj = jsonParse(str);\n    if (obj instanceof Error) {\n      return null;\n    }\n  } else {\n    // assume it's an Object\n    obj = str;\n  }\n\n  var c = new Cookie();\n  for (var i=0; i<Cookie.serializableProperties.length; i++) {\n    var prop = Cookie.serializableProperties[i];\n    if (obj[prop] === undefined ||\n        obj[prop] === Cookie.prototype[prop])\n    {\n      continue; // leave as prototype default\n    }\n\n    if (prop === 'expires' ||\n        prop === 'creation' ||\n        prop === 'lastAccessed')\n    {\n      if (obj[prop] === null) {\n        c[prop] = null;\n      } else {\n        c[prop] = obj[prop] == \"Infinity\" ?\n          \"Infinity\" : new Date(obj[prop]);\n      }\n    } else {\n      c[prop] = obj[prop];\n    }\n  }\n\n  return c;\n}\n\n/* Section 5.4 part 2:\n * \"*  Cookies with longer paths are listed before cookies with\n *     shorter paths.\n *\n *  *  Among cookies that have equal-length path fields, cookies with\n *     earlier creation-times are listed before cookies with later\n *     creation-times.\"\n */\n\nfunction cookieCompare(a,b) {\n  var cmp = 0;\n\n  // descending for length: b CMP a\n  var aPathLen = a.path ? a.path.length : 0;\n  var bPathLen = b.path ? b.path.length : 0;\n  cmp = bPathLen - aPathLen;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  // ascending for time: a CMP b\n  var aTime = a.creation ? a.creation.getTime() : MAX_TIME;\n  var bTime = b.creation ? b.creation.getTime() : MAX_TIME;\n  cmp = aTime - bTime;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  // break ties for the same millisecond (precision of JavaScript's clock)\n  cmp = a.creationIndex - b.creationIndex;\n\n  return cmp;\n}\n\n// Gives the permutation of all possible pathMatch()es of a given path. The\n// array is in longest-to-shortest order.  Handy for indexing.\nfunction permutePath(path) {\n  if (path === '/') {\n    return ['/'];\n  }\n  if (path.lastIndexOf('/') === path.length-1) {\n    path = path.substr(0,path.length-1);\n  }\n  var permutations = [path];\n  while (path.length > 1) {\n    var lindex = path.lastIndexOf('/');\n    if (lindex === 0) {\n      break;\n    }\n    path = path.substr(0,lindex);\n    permutations.push(path);\n  }\n  permutations.push('/');\n  return permutations;\n}\n\nfunction getCookieContext(url) {\n  if (url instanceof Object) {\n    return url;\n  }\n  // NOTE: decodeURI will throw on malformed URIs (see GH-32).\n  // Therefore, we will just skip decoding for such URIs.\n  try {\n    url = decodeURI(url);\n  }\n  catch(err) {\n    // Silently swallow error\n  }\n\n  return urlParse(url);\n}\n\nfunction Cookie(options) {\n  options = options || {};\n\n  Object.keys(options).forEach(function(prop) {\n    if (Cookie.prototype.hasOwnProperty(prop) &&\n        Cookie.prototype[prop] !== options[prop] &&\n        prop.substr(0,1) !== '_')\n    {\n      this[prop] = options[prop];\n    }\n  }, this);\n\n  this.creation = this.creation || new Date();\n\n  // used to break creation ties in cookieCompare():\n  Object.defineProperty(this, 'creationIndex', {\n    configurable: false,\n    enumerable: false, // important for assert.deepEqual checks\n    writable: true,\n    value: ++Cookie.cookiesCreated\n  });\n}\n\nCookie.cookiesCreated = 0; // incremented each time a cookie is created\n\nCookie.parse = parse;\nCookie.fromJSON = fromJSON;\n\nCookie.prototype.key = \"\";\nCookie.prototype.value = \"\";\n\n// the order in which the RFC has them:\nCookie.prototype.expires = \"Infinity\"; // coerces to literal Infinity\nCookie.prototype.maxAge = null; // takes precedence over expires for TTL\nCookie.prototype.domain = null;\nCookie.prototype.path = null;\nCookie.prototype.secure = false;\nCookie.prototype.httpOnly = false;\nCookie.prototype.extensions = null;\n\n// set by the CookieJar:\nCookie.prototype.hostOnly = null; // boolean when set\nCookie.prototype.pathIsDefault = null; // boolean when set\nCookie.prototype.creation = null; // Date when set; defaulted by Cookie.parse\nCookie.prototype.lastAccessed = null; // Date when set\nObject.defineProperty(Cookie.prototype, 'creationIndex', {\n  configurable: true,\n  enumerable: false,\n  writable: true,\n  value: 0\n});\n\nCookie.serializableProperties = Object.keys(Cookie.prototype)\n  .filter(function(prop) {\n    return !(\n      Cookie.prototype[prop] instanceof Function ||\n      prop === 'creationIndex' ||\n      prop.substr(0,1) === '_'\n    );\n  });\n\nCookie.prototype.inspect = function inspect() {\n  var now = Date.now();\n  return 'Cookie=\"'+this.toString() +\n    '; hostOnly='+(this.hostOnly != null ? this.hostOnly : '?') +\n    '; aAge='+(this.lastAccessed ? (now-this.lastAccessed.getTime())+'ms' : '?') +\n    '; cAge='+(this.creation ? (now-this.creation.getTime())+'ms' : '?') +\n    '\"';\n};\n\n// Use the new custom inspection symbol to add the custom inspect function if\n// available.\nif (util.inspect.custom) {\n  Cookie.prototype[util.inspect.custom] = Cookie.prototype.inspect;\n}\n\nCookie.prototype.toJSON = function() {\n  var obj = {};\n\n  var props = Cookie.serializableProperties;\n  for (var i=0; i<props.length; i++) {\n    var prop = props[i];\n    if (this[prop] === Cookie.prototype[prop]) {\n      continue; // leave as prototype default\n    }\n\n    if (prop === 'expires' ||\n        prop === 'creation' ||\n        prop === 'lastAccessed')\n    {\n      if (this[prop] === null) {\n        obj[prop] = null;\n      } else {\n        obj[prop] = this[prop] == \"Infinity\" ? // intentionally not ===\n          \"Infinity\" : this[prop].toISOString();\n      }\n    } else if (prop === 'maxAge') {\n      if (this[prop] !== null) {\n        // again, intentionally not ===\n        obj[prop] = (this[prop] == Infinity || this[prop] == -Infinity) ?\n          this[prop].toString() : this[prop];\n      }\n    } else {\n      if (this[prop] !== Cookie.prototype[prop]) {\n        obj[prop] = this[prop];\n      }\n    }\n  }\n\n  return obj;\n};\n\nCookie.prototype.clone = function() {\n  return fromJSON(this.toJSON());\n};\n\nCookie.prototype.validate = function validate() {\n  if (!COOKIE_OCTETS.test(this.value)) {\n    return false;\n  }\n  if (this.expires != Infinity && !(this.expires instanceof Date) && !parseDate(this.expires)) {\n    return false;\n  }\n  if (this.maxAge != null && this.maxAge <= 0) {\n    return false; // \"Max-Age=\" non-zero-digit *DIGIT\n  }\n  if (this.path != null && !PATH_VALUE.test(this.path)) {\n    return false;\n  }\n\n  var cdomain = this.cdomain();\n  if (cdomain) {\n    if (cdomain.match(/\\.$/)) {\n      return false; // S4.1.2.3 suggests that this is bad. domainMatch() tests confirm this\n    }\n    var suffix = pubsuffix.getPublicSuffix(cdomain);\n    if (suffix == null) { // it's a public suffix\n      return false;\n    }\n  }\n  return true;\n};\n\nCookie.prototype.setExpires = function setExpires(exp) {\n  if (exp instanceof Date) {\n    this.expires = exp;\n  } else {\n    this.expires = parseDate(exp) || \"Infinity\";\n  }\n};\n\nCookie.prototype.setMaxAge = function setMaxAge(age) {\n  if (age === Infinity || age === -Infinity) {\n    this.maxAge = age.toString(); // so JSON.stringify() works\n  } else {\n    this.maxAge = age;\n  }\n};\n\n// gives Cookie header format\nCookie.prototype.cookieString = function cookieString() {\n  var val = this.value;\n  if (val == null) {\n    val = '';\n  }\n  if (this.key === '') {\n    return val;\n  }\n  return this.key+'='+val;\n};\n\n// gives Set-Cookie header format\nCookie.prototype.toString = function toString() {\n  var str = this.cookieString();\n\n  if (this.expires != Infinity) {\n    if (this.expires instanceof Date) {\n      str += '; Expires='+formatDate(this.expires);\n    } else {\n      str += '; Expires='+this.expires;\n    }\n  }\n\n  if (this.maxAge != null && this.maxAge != Infinity) {\n    str += '; Max-Age='+this.maxAge;\n  }\n\n  if (this.domain && !this.hostOnly) {\n    str += '; Domain='+this.domain;\n  }\n  if (this.path) {\n    str += '; Path='+this.path;\n  }\n\n  if (this.secure) {\n    str += '; Secure';\n  }\n  if (this.httpOnly) {\n    str += '; HttpOnly';\n  }\n  if (this.extensions) {\n    this.extensions.forEach(function(ext) {\n      str += '; '+ext;\n    });\n  }\n\n  return str;\n};\n\n// TTL() partially replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n// elsewhere)\n// S5.3 says to give the \"latest representable date\" for which we use Infinity\n// For \"expired\" we use 0\nCookie.prototype.TTL = function TTL(now) {\n  /* RFC6265 S4.1.2.2 If a cookie has both the Max-Age and the Expires\n   * attribute, the Max-Age attribute has precedence and controls the\n   * expiration date of the cookie.\n   * (Concurs with S5.3 step 3)\n   */\n  if (this.maxAge != null) {\n    return this.maxAge<=0 ? 0 : this.maxAge*1000;\n  }\n\n  var expires = this.expires;\n  if (expires != Infinity) {\n    if (!(expires instanceof Date)) {\n      expires = parseDate(expires) || Infinity;\n    }\n\n    if (expires == Infinity) {\n      return Infinity;\n    }\n\n    return expires.getTime() - (now || Date.now());\n  }\n\n  return Infinity;\n};\n\n// expiryTime() replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n// elsewhere)\nCookie.prototype.expiryTime = function expiryTime(now) {\n  if (this.maxAge != null) {\n    var relativeTo = now || this.creation || new Date();\n    var age = (this.maxAge <= 0) ? -Infinity : this.maxAge*1000;\n    return relativeTo.getTime() + age;\n  }\n\n  if (this.expires == Infinity) {\n    return Infinity;\n  }\n  return this.expires.getTime();\n};\n\n// expiryDate() replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n// elsewhere), except it returns a Date\nCookie.prototype.expiryDate = function expiryDate(now) {\n  var millisec = this.expiryTime(now);\n  if (millisec == Infinity) {\n    return new Date(MAX_TIME);\n  } else if (millisec == -Infinity) {\n    return new Date(MIN_TIME);\n  } else {\n    return new Date(millisec);\n  }\n};\n\n// This replaces the \"persistent-flag\" parts of S5.3 step 3\nCookie.prototype.isPersistent = function isPersistent() {\n  return (this.maxAge != null || this.expires != Infinity);\n};\n\n// Mostly S5.1.2 and S5.2.3:\nCookie.prototype.cdomain =\nCookie.prototype.canonicalizedDomain = function canonicalizedDomain() {\n  if (this.domain == null) {\n    return null;\n  }\n  return canonicalDomain(this.domain);\n};\n\nfunction CookieJar(store, options) {\n  if (typeof options === \"boolean\") {\n    options = {rejectPublicSuffixes: options};\n  } else if (options == null) {\n    options = {};\n  }\n  if (options.rejectPublicSuffixes != null) {\n    this.rejectPublicSuffixes = options.rejectPublicSuffixes;\n  }\n  if (options.looseMode != null) {\n    this.enableLooseMode = options.looseMode;\n  }\n\n  if (!store) {\n    store = new MemoryCookieStore();\n  }\n  this.store = store;\n}\nCookieJar.prototype.store = null;\nCookieJar.prototype.rejectPublicSuffixes = true;\nCookieJar.prototype.enableLooseMode = false;\nvar CAN_BE_SYNC = [];\n\nCAN_BE_SYNC.push('setCookie');\nCookieJar.prototype.setCookie = function(cookie, url, options, cb) {\n  var err;\n  var context = getCookieContext(url);\n  if (options instanceof Function) {\n    cb = options;\n    options = {};\n  }\n\n  var host = canonicalDomain(context.hostname);\n  var loose = this.enableLooseMode;\n  if (options.loose != null) {\n    loose = options.loose;\n  }\n\n  // S5.3 step 1\n  if (!(cookie instanceof Cookie)) {\n    cookie = Cookie.parse(cookie, { loose: loose });\n  }\n  if (!cookie) {\n    err = new Error(\"Cookie failed to parse\");\n    return cb(options.ignoreError ? null : err);\n  }\n\n  // S5.3 step 2\n  var now = options.now || new Date(); // will assign later to save effort in the face of errors\n\n  // S5.3 step 3: NOOP; persistent-flag and expiry-time is handled by getCookie()\n\n  // S5.3 step 4: NOOP; domain is null by default\n\n  // S5.3 step 5: public suffixes\n  if (this.rejectPublicSuffixes && cookie.domain) {\n    var suffix = pubsuffix.getPublicSuffix(cookie.cdomain());\n    if (suffix == null) { // e.g. \"com\"\n      err = new Error(\"Cookie has domain set to a public suffix\");\n      return cb(options.ignoreError ? null : err);\n    }\n  }\n\n  // S5.3 step 6:\n  if (cookie.domain) {\n    if (!domainMatch(host, cookie.cdomain(), false)) {\n      err = new Error(\"Cookie not in this host's domain. Cookie:\"+cookie.cdomain()+\" Request:\"+host);\n      return cb(options.ignoreError ? null : err);\n    }\n\n    if (cookie.hostOnly == null) { // don't reset if already set\n      cookie.hostOnly = false;\n    }\n\n  } else {\n    cookie.hostOnly = true;\n    cookie.domain = host;\n  }\n\n  //S5.2.4 If the attribute-value is empty or if the first character of the\n  //attribute-value is not %x2F (\"/\"):\n  //Let cookie-path be the default-path.\n  if (!cookie.path || cookie.path[0] !== '/') {\n    cookie.path = defaultPath(context.pathname);\n    cookie.pathIsDefault = true;\n  }\n\n  // S5.3 step 8: NOOP; secure attribute\n  // S5.3 step 9: NOOP; httpOnly attribute\n\n  // S5.3 step 10\n  if (options.http === false && cookie.httpOnly) {\n    err = new Error(\"Cookie is HttpOnly and this isn't an HTTP API\");\n    return cb(options.ignoreError ? null : err);\n  }\n\n  var store = this.store;\n\n  if (!store.updateCookie) {\n    store.updateCookie = function(oldCookie, newCookie, cb) {\n      this.putCookie(newCookie, cb);\n    };\n  }\n\n  function withCookie(err, oldCookie) {\n    if (err) {\n      return cb(err);\n    }\n\n    var next = function(err) {\n      if (err) {\n        return cb(err);\n      } else {\n        cb(null, cookie);\n      }\n    };\n\n    if (oldCookie) {\n      // S5.3 step 11 - \"If the cookie store contains a cookie with the same name,\n      // domain, and path as the newly created cookie:\"\n      if (options.http === false && oldCookie.httpOnly) { // step 11.2\n        err = new Error(\"old Cookie is HttpOnly and this isn't an HTTP API\");\n        return cb(options.ignoreError ? null : err);\n      }\n      cookie.creation = oldCookie.creation; // step 11.3\n      cookie.creationIndex = oldCookie.creationIndex; // preserve tie-breaker\n      cookie.lastAccessed = now;\n      // Step 11.4 (delete cookie) is implied by just setting the new one:\n      store.updateCookie(oldCookie, cookie, next); // step 12\n\n    } else {\n      cookie.creation = cookie.lastAccessed = now;\n      store.putCookie(cookie, next); // step 12\n    }\n  }\n\n  store.findCookie(cookie.domain, cookie.path, cookie.key, withCookie);\n};\n\n// RFC6365 S5.4\nCAN_BE_SYNC.push('getCookies');\nCookieJar.prototype.getCookies = function(url, options, cb) {\n  var context = getCookieContext(url);\n  if (options instanceof Function) {\n    cb = options;\n    options = {};\n  }\n\n  var host = canonicalDomain(context.hostname);\n  var path = context.pathname || '/';\n\n  var secure = options.secure;\n  if (secure == null && context.protocol &&\n      (context.protocol == 'https:' || context.protocol == 'wss:'))\n  {\n    secure = true;\n  }\n\n  var http = options.http;\n  if (http == null) {\n    http = true;\n  }\n\n  var now = options.now || Date.now();\n  var expireCheck = options.expire !== false;\n  var allPaths = !!options.allPaths;\n  var store = this.store;\n\n  function matchingCookie(c) {\n    // \"Either:\n    //   The cookie's host-only-flag is true and the canonicalized\n    //   request-host is identical to the cookie's domain.\n    // Or:\n    //   The cookie's host-only-flag is false and the canonicalized\n    //   request-host domain-matches the cookie's domain.\"\n    if (c.hostOnly) {\n      if (c.domain != host) {\n        return false;\n      }\n    } else {\n      if (!domainMatch(host, c.domain, false)) {\n        return false;\n      }\n    }\n\n    // \"The request-uri's path path-matches the cookie's path.\"\n    if (!allPaths && !pathMatch(path, c.path)) {\n      return false;\n    }\n\n    // \"If the cookie's secure-only-flag is true, then the request-uri's\n    // scheme must denote a \"secure\" protocol\"\n    if (c.secure && !secure) {\n      return false;\n    }\n\n    // \"If the cookie's http-only-flag is true, then exclude the cookie if the\n    // cookie-string is being generated for a \"non-HTTP\" API\"\n    if (c.httpOnly && !http) {\n      return false;\n    }\n\n    // deferred from S5.3\n    // non-RFC: allow retention of expired cookies by choice\n    if (expireCheck && c.expiryTime() <= now) {\n      store.removeCookie(c.domain, c.path, c.key, function(){}); // result ignored\n      return false;\n    }\n\n    return true;\n  }\n\n  store.findCookies(host, allPaths ? null : path, function(err,cookies) {\n    if (err) {\n      return cb(err);\n    }\n\n    cookies = cookies.filter(matchingCookie);\n\n    // sorting of S5.4 part 2\n    if (options.sort !== false) {\n      cookies = cookies.sort(cookieCompare);\n    }\n\n    // S5.4 part 3\n    var now = new Date();\n    cookies.forEach(function(c) {\n      c.lastAccessed = now;\n    });\n    // TODO persist lastAccessed\n\n    cb(null,cookies);\n  });\n};\n\nCAN_BE_SYNC.push('getCookieString');\nCookieJar.prototype.getCookieString = function(/*..., cb*/) {\n  var args = Array.prototype.slice.call(arguments,0);\n  var cb = args.pop();\n  var next = function(err,cookies) {\n    if (err) {\n      cb(err);\n    } else {\n      cb(null, cookies\n        .sort(cookieCompare)\n        .map(function(c){\n          return c.cookieString();\n        })\n        .join('; '));\n    }\n  };\n  args.push(next);\n  this.getCookies.apply(this,args);\n};\n\nCAN_BE_SYNC.push('getSetCookieStrings');\nCookieJar.prototype.getSetCookieStrings = function(/*..., cb*/) {\n  var args = Array.prototype.slice.call(arguments,0);\n  var cb = args.pop();\n  var next = function(err,cookies) {\n    if (err) {\n      cb(err);\n    } else {\n      cb(null, cookies.map(function(c){\n        return c.toString();\n      }));\n    }\n  };\n  args.push(next);\n  this.getCookies.apply(this,args);\n};\n\nCAN_BE_SYNC.push('serialize');\nCookieJar.prototype.serialize = function(cb) {\n  var type = this.store.constructor.name;\n  if (type === 'Object') {\n    type = null;\n  }\n\n  // update README.md \"Serialization Format\" if you change this, please!\n  var serialized = {\n    // The version of tough-cookie that serialized this jar. Generally a good\n    // practice since future versions can make data import decisions based on\n    // known past behavior. When/if this matters, use `semver`.\n    version: 'tough-cookie@'+VERSION,\n\n    // add the store type, to make humans happy:\n    storeType: type,\n\n    // CookieJar configuration:\n    rejectPublicSuffixes: !!this.rejectPublicSuffixes,\n\n    // this gets filled from getAllCookies:\n    cookies: []\n  };\n\n  if (!(this.store.getAllCookies &&\n        typeof this.store.getAllCookies === 'function'))\n  {\n    return cb(new Error('store does not support getAllCookies and cannot be serialized'));\n  }\n\n  this.store.getAllCookies(function(err,cookies) {\n    if (err) {\n      return cb(err);\n    }\n\n    serialized.cookies = cookies.map(function(cookie) {\n      // convert to serialized 'raw' cookies\n      cookie = (cookie instanceof Cookie) ? cookie.toJSON() : cookie;\n\n      // Remove the index so new ones get assigned during deserialization\n      delete cookie.creationIndex;\n\n      return cookie;\n    });\n\n    return cb(null, serialized);\n  });\n};\n\n// well-known name that JSON.stringify calls\nCookieJar.prototype.toJSON = function() {\n  return this.serializeSync();\n};\n\n// use the class method CookieJar.deserialize instead of calling this directly\nCAN_BE_SYNC.push('_importCookies');\nCookieJar.prototype._importCookies = function(serialized, cb) {\n  var jar = this;\n  var cookies = serialized.cookies;\n  if (!cookies || !Array.isArray(cookies)) {\n    return cb(new Error('serialized jar has no cookies array'));\n  }\n  cookies = cookies.slice(); // do not modify the original\n\n  function putNext(err) {\n    if (err) {\n      return cb(err);\n    }\n\n    if (!cookies.length) {\n      return cb(err, jar);\n    }\n\n    var cookie;\n    try {\n      cookie = fromJSON(cookies.shift());\n    } catch (e) {\n      return cb(e);\n    }\n\n    if (cookie === null) {\n      return putNext(null); // skip this cookie\n    }\n\n    jar.store.putCookie(cookie, putNext);\n  }\n\n  putNext();\n};\n\nCookieJar.deserialize = function(strOrObj, store, cb) {\n  if (arguments.length !== 3) {\n    // store is optional\n    cb = store;\n    store = null;\n  }\n\n  var serialized;\n  if (typeof strOrObj === 'string') {\n    serialized = jsonParse(strOrObj);\n    if (serialized instanceof Error) {\n      return cb(serialized);\n    }\n  } else {\n    serialized = strOrObj;\n  }\n\n  var jar = new CookieJar(store, serialized.rejectPublicSuffixes);\n  jar._importCookies(serialized, function(err) {\n    if (err) {\n      return cb(err);\n    }\n    cb(null, jar);\n  });\n};\n\nCookieJar.deserializeSync = function(strOrObj, store) {\n  var serialized = typeof strOrObj === 'string' ?\n    JSON.parse(strOrObj) : strOrObj;\n  var jar = new CookieJar(store, serialized.rejectPublicSuffixes);\n\n  // catch this mistake early:\n  if (!jar.store.synchronous) {\n    throw new Error('CookieJar store is not synchronous; use async API instead.');\n  }\n\n  jar._importCookiesSync(serialized);\n  return jar;\n};\nCookieJar.fromJSON = CookieJar.deserializeSync;\n\nCookieJar.prototype.clone = function(newStore, cb) {\n  if (arguments.length === 1) {\n    cb = newStore;\n    newStore = null;\n  }\n\n  this.serialize(function(err,serialized) {\n    if (err) {\n      return cb(err);\n    }\n    CookieJar.deserialize(serialized, newStore, cb);\n  });\n};\n\nCAN_BE_SYNC.push('removeAllCookies');\nCookieJar.prototype.removeAllCookies = function(cb) {\n  var store = this.store;\n\n  // Check that the store implements its own removeAllCookies(). The default\n  // implementation in Store will immediately call the callback with a \"not\n  // implemented\" Error.\n  if (store.removeAllCookies instanceof Function &&\n      store.removeAllCookies !== Store.prototype.removeAllCookies)\n  {\n    return store.removeAllCookies(cb);\n  }\n\n  store.getAllCookies(function(err, cookies) {\n    if (err) {\n      return cb(err);\n    }\n\n    if (cookies.length === 0) {\n      return cb(null);\n    }\n\n    var completedCount = 0;\n    var removeErrors = [];\n\n    function removeCookieCb(removeErr) {\n      if (removeErr) {\n        removeErrors.push(removeErr);\n      }\n\n      completedCount++;\n\n      if (completedCount === cookies.length) {\n        return cb(removeErrors.length ? removeErrors[0] : null);\n      }\n    }\n\n    cookies.forEach(function(cookie) {\n      store.removeCookie(cookie.domain, cookie.path, cookie.key, removeCookieCb);\n    });\n  });\n};\n\nCookieJar.prototype._cloneSync = syncWrap('clone');\nCookieJar.prototype.cloneSync = function(newStore) {\n  if (!newStore.synchronous) {\n    throw new Error('CookieJar clone destination store is not synchronous; use async API instead.');\n  }\n  return this._cloneSync(newStore);\n};\n\n// Use a closure to provide a true imperative API for synchronous stores.\nfunction syncWrap(method) {\n  return function() {\n    if (!this.store.synchronous) {\n      throw new Error('CookieJar store is not synchronous; use async API instead.');\n    }\n\n    var args = Array.prototype.slice.call(arguments);\n    var syncErr, syncResult;\n    args.push(function syncCb(err, result) {\n      syncErr = err;\n      syncResult = result;\n    });\n    this[method].apply(this, args);\n\n    if (syncErr) {\n      throw syncErr;\n    }\n    return syncResult;\n  };\n}\n\n// wrap all declared CAN_BE_SYNC methods in the sync wrapper\nCAN_BE_SYNC.forEach(function(method) {\n  CookieJar.prototype[method+'Sync'] = syncWrap(method);\n});\n\nexports.version = VERSION;\nexports.CookieJar = CookieJar;\nexports.Cookie = Cookie;\nexports.Store = Store;\nexports.MemoryCookieStore = MemoryCookieStore;\nexports.parseDate = parseDate;\nexports.formatDate = formatDate;\nexports.parse = parse;\nexports.fromJSON = fromJSON;\nexports.domainMatch = domainMatch;\nexports.defaultPath = defaultPath;\nexports.pathMatch = pathMatch;\nexports.getPublicSuffix = pubsuffix.getPublicSuffix;\nexports.cookieCompare = cookieCompare;\nexports.permuteDomain = __webpack_require__(/*! ./permuteDomain */ \"(rsc)/./node_modules/tough-cookie/lib/permuteDomain.js\").permuteDomain;\nexports.permutePath = permutePath;\nexports.canonicalDomain = canonicalDomain;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/cookie.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/memstore.js":
/*!***************************************************!*\
  !*** ./node_modules/tough-cookie/lib/memstore.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar Store = (__webpack_require__(/*! ./store */ \"(rsc)/./node_modules/tough-cookie/lib/store.js\").Store);\nvar permuteDomain = (__webpack_require__(/*! ./permuteDomain */ \"(rsc)/./node_modules/tough-cookie/lib/permuteDomain.js\").permuteDomain);\nvar pathMatch = (__webpack_require__(/*! ./pathMatch */ \"(rsc)/./node_modules/tough-cookie/lib/pathMatch.js\").pathMatch);\nvar util = __webpack_require__(/*! util */ \"util\");\n\nfunction MemoryCookieStore() {\n  Store.call(this);\n  this.idx = {};\n}\nutil.inherits(MemoryCookieStore, Store);\nexports.MemoryCookieStore = MemoryCookieStore;\nMemoryCookieStore.prototype.idx = null;\n\n// Since it's just a struct in RAM, this Store is synchronous\nMemoryCookieStore.prototype.synchronous = true;\n\n// force a default depth:\nMemoryCookieStore.prototype.inspect = function() {\n  return \"{ idx: \"+util.inspect(this.idx, false, 2)+' }';\n};\n\n// Use the new custom inspection symbol to add the custom inspect function if\n// available.\nif (util.inspect.custom) {\n  MemoryCookieStore.prototype[util.inspect.custom] = MemoryCookieStore.prototype.inspect;\n}\n\nMemoryCookieStore.prototype.findCookie = function(domain, path, key, cb) {\n  if (!this.idx[domain]) {\n    return cb(null,undefined);\n  }\n  if (!this.idx[domain][path]) {\n    return cb(null,undefined);\n  }\n  return cb(null,this.idx[domain][path][key]||null);\n};\n\nMemoryCookieStore.prototype.findCookies = function(domain, path, cb) {\n  var results = [];\n  if (!domain) {\n    return cb(null,[]);\n  }\n\n  var pathMatcher;\n  if (!path) {\n    // null means \"all paths\"\n    pathMatcher = function matchAll(domainIndex) {\n      for (var curPath in domainIndex) {\n        var pathIndex = domainIndex[curPath];\n        for (var key in pathIndex) {\n          results.push(pathIndex[key]);\n        }\n      }\n    };\n\n  } else {\n    pathMatcher = function matchRFC(domainIndex) {\n       //NOTE: we should use path-match algorithm from S5.1.4 here\n       //(see : https://github.com/ChromiumWebApps/chromium/blob/b3d3b4da8bb94c1b2e061600df106d590fda3620/net/cookies/canonical_cookie.cc#L299)\n       Object.keys(domainIndex).forEach(function (cookiePath) {\n         if (pathMatch(path, cookiePath)) {\n           var pathIndex = domainIndex[cookiePath];\n\n           for (var key in pathIndex) {\n             results.push(pathIndex[key]);\n           }\n         }\n       });\n     };\n  }\n\n  var domains = permuteDomain(domain) || [domain];\n  var idx = this.idx;\n  domains.forEach(function(curDomain) {\n    var domainIndex = idx[curDomain];\n    if (!domainIndex) {\n      return;\n    }\n    pathMatcher(domainIndex);\n  });\n\n  cb(null,results);\n};\n\nMemoryCookieStore.prototype.putCookie = function(cookie, cb) {\n  if (!this.idx[cookie.domain]) {\n    this.idx[cookie.domain] = {};\n  }\n  if (!this.idx[cookie.domain][cookie.path]) {\n    this.idx[cookie.domain][cookie.path] = {};\n  }\n  this.idx[cookie.domain][cookie.path][cookie.key] = cookie;\n  cb(null);\n};\n\nMemoryCookieStore.prototype.updateCookie = function(oldCookie, newCookie, cb) {\n  // updateCookie() may avoid updating cookies that are identical.  For example,\n  // lastAccessed may not be important to some stores and an equality\n  // comparison could exclude that field.\n  this.putCookie(newCookie,cb);\n};\n\nMemoryCookieStore.prototype.removeCookie = function(domain, path, key, cb) {\n  if (this.idx[domain] && this.idx[domain][path] && this.idx[domain][path][key]) {\n    delete this.idx[domain][path][key];\n  }\n  cb(null);\n};\n\nMemoryCookieStore.prototype.removeCookies = function(domain, path, cb) {\n  if (this.idx[domain]) {\n    if (path) {\n      delete this.idx[domain][path];\n    } else {\n      delete this.idx[domain];\n    }\n  }\n  return cb(null);\n};\n\nMemoryCookieStore.prototype.removeAllCookies = function(cb) {\n  this.idx = {};\n  return cb(null);\n}\n\nMemoryCookieStore.prototype.getAllCookies = function(cb) {\n  var cookies = [];\n  var idx = this.idx;\n\n  var domains = Object.keys(idx);\n  domains.forEach(function(domain) {\n    var paths = Object.keys(idx[domain]);\n    paths.forEach(function(path) {\n      var keys = Object.keys(idx[domain][path]);\n      keys.forEach(function(key) {\n        if (key !== null) {\n          cookies.push(idx[domain][path][key]);\n        }\n      });\n    });\n  });\n\n  // Sort by creationIndex so deserializing retains the creation order.\n  // When implementing your own store, this SHOULD retain the order too\n  cookies.sort(function(a,b) {\n    return (a.creationIndex||0) - (b.creationIndex||0);\n  });\n\n  cb(null, cookies);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/memstore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/pathMatch.js":
/*!****************************************************!*\
  !*** ./node_modules/tough-cookie/lib/pathMatch.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\n/*\n * \"A request-path path-matches a given cookie-path if at least one of the\n * following conditions holds:\"\n */\nfunction pathMatch (reqPath, cookiePath) {\n  // \"o  The cookie-path and the request-path are identical.\"\n  if (cookiePath === reqPath) {\n    return true;\n  }\n\n  var idx = reqPath.indexOf(cookiePath);\n  if (idx === 0) {\n    // \"o  The cookie-path is a prefix of the request-path, and the last\n    // character of the cookie-path is %x2F (\"/\").\"\n    if (cookiePath.substr(-1) === \"/\") {\n      return true;\n    }\n\n    // \" o  The cookie-path is a prefix of the request-path, and the first\n    // character of the request-path that is not included in the cookie- path\n    // is a %x2F (\"/\") character.\"\n    if (reqPath.substr(cookiePath.length, 1) === \"/\") {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nexports.pathMatch = pathMatch;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/pathMatch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/permuteDomain.js":
/*!********************************************************!*\
  !*** ./node_modules/tough-cookie/lib/permuteDomain.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar pubsuffix = __webpack_require__(/*! ./pubsuffix-psl */ \"(rsc)/./node_modules/tough-cookie/lib/pubsuffix-psl.js\");\n\n// Gives the permutation of all possible domainMatch()es of a given domain. The\n// array is in shortest-to-longest order.  Handy for indexing.\nfunction permuteDomain (domain) {\n  var pubSuf = pubsuffix.getPublicSuffix(domain);\n  if (!pubSuf) {\n    return null;\n  }\n  if (pubSuf == domain) {\n    return [domain];\n  }\n\n  var prefix = domain.slice(0, -(pubSuf.length + 1)); // \".example.com\"\n  var parts = prefix.split('.').reverse();\n  var cur = pubSuf;\n  var permutations = [cur];\n  while (parts.length) {\n    cur = parts.shift() + '.' + cur;\n    permutations.push(cur);\n  }\n  return permutations;\n}\n\nexports.permuteDomain = permuteDomain;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/permuteDomain.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/pubsuffix-psl.js":
/*!********************************************************!*\
  !*** ./node_modules/tough-cookie/lib/pubsuffix-psl.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/*!\n * Copyright (c) 2018, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar psl = __webpack_require__(/*! psl */ \"(rsc)/./node_modules/psl/index.js\");\n\nfunction getPublicSuffix(domain) {\n  return psl.get(domain);\n}\n\nexports.getPublicSuffix = getPublicSuffix;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/pubsuffix-psl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/store.js":
/*!************************************************!*\
  !*** ./node_modules/tough-cookie/lib/store.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\n/*jshint unused:false */\n\nfunction Store() {\n}\nexports.Store = Store;\n\n// Stores may be synchronous, but are still required to use a\n// Continuation-Passing Style API.  The CookieJar itself will expose a \"*Sync\"\n// API that converts from synchronous-callbacks to imperative style.\nStore.prototype.synchronous = false;\n\nStore.prototype.findCookie = function(domain, path, key, cb) {\n  throw new Error('findCookie is not implemented');\n};\n\nStore.prototype.findCookies = function(domain, path, cb) {\n  throw new Error('findCookies is not implemented');\n};\n\nStore.prototype.putCookie = function(cookie, cb) {\n  throw new Error('putCookie is not implemented');\n};\n\nStore.prototype.updateCookie = function(oldCookie, newCookie, cb) {\n  // recommended default implementation:\n  // return this.putCookie(newCookie, cb);\n  throw new Error('updateCookie is not implemented');\n};\n\nStore.prototype.removeCookie = function(domain, path, key, cb) {\n  throw new Error('removeCookie is not implemented');\n};\n\nStore.prototype.removeCookies = function(domain, path, cb) {\n  throw new Error('removeCookies is not implemented');\n};\n\nStore.prototype.removeAllCookies = function(cb) {\n  throw new Error('removeAllCookies is not implemented');\n}\n\nStore.prototype.getAllCookies = function(cb) {\n  throw new Error('getAllCookies is not implemented (therefore jar cannot be serialized)');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/store.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/version.js":
/*!**************************************************!*\
  !*** ./node_modules/tough-cookie/lib/version.js ***!
  \**************************************************/
/***/ ((module) => {

eval("// generated by genversion\nmodule.exports = '2.5.0'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdG91Z2gtY29va2llL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYXpvcnBheS1uZXh0Ly4vbm9kZV9tb2R1bGVzL3RvdWdoLWNvb2tpZS9saWIvdmVyc2lvbi5qcz8yMTgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGdlbmVyYXRlZCBieSBnZW52ZXJzaW9uXG5tb2R1bGUuZXhwb3J0cyA9ICcyLjUuMCdcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/version.js\n");

/***/ })

};
;