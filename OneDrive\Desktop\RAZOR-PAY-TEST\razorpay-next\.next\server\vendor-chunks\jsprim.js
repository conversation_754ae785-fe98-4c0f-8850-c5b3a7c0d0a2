/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsprim";
exports.ids = ["vendor-chunks/jsprim"];
exports.modules = {

/***/ "(rsc)/./node_modules/jsprim/lib/jsprim.js":
/*!*******************************************!*\
  !*** ./node_modules/jsprim/lib/jsprim.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/*\n * lib/jsprim.js: utilities for primitive JavaScript types\n */\n\nvar mod_assert = __webpack_require__(/*! assert-plus */ \"(rsc)/./node_modules/assert-plus/assert.js\");\nvar mod_util = __webpack_require__(/*! util */ \"util\");\n\nvar mod_extsprintf = __webpack_require__(/*! extsprintf */ \"(rsc)/./node_modules/extsprintf/lib/extsprintf.js\");\nvar mod_verror = __webpack_require__(/*! verror */ \"(rsc)/./node_modules/verror/lib/verror.js\");\nvar mod_jsonschema = __webpack_require__(/*! json-schema */ \"(rsc)/./node_modules/json-schema/lib/validate.js\");\n\n/*\n * Public interface\n */\nexports.deepCopy = deepCopy;\nexports.deepEqual = deepEqual;\nexports.isEmpty = isEmpty;\nexports.hasKey = hasKey;\nexports.forEachKey = forEachKey;\nexports.pluck = pluck;\nexports.flattenObject = flattenObject;\nexports.flattenIter = flattenIter;\nexports.validateJsonObject = validateJsonObjectJS;\nexports.validateJsonObjectJS = validateJsonObjectJS;\nexports.randElt = randElt;\nexports.extraProperties = extraProperties;\nexports.mergeObjects = mergeObjects;\n\nexports.startsWith = startsWith;\nexports.endsWith = endsWith;\n\nexports.parseInteger = parseInteger;\n\nexports.iso8601 = iso8601;\nexports.rfc1123 = rfc1123;\nexports.parseDateTime = parseDateTime;\n\nexports.hrtimediff = hrtimeDiff;\nexports.hrtimeDiff = hrtimeDiff;\nexports.hrtimeAccum = hrtimeAccum;\nexports.hrtimeAdd = hrtimeAdd;\nexports.hrtimeNanosec = hrtimeNanosec;\nexports.hrtimeMicrosec = hrtimeMicrosec;\nexports.hrtimeMillisec = hrtimeMillisec;\n\n\n/*\n * Deep copy an acyclic *basic* Javascript object.  This only handles basic\n * scalars (strings, numbers, booleans) and arbitrarily deep arrays and objects\n * containing these.  This does *not* handle instances of other classes.\n */\nfunction deepCopy(obj)\n{\n\tvar ret, key;\n\tvar marker = '__deepCopy';\n\n\tif (obj && obj[marker])\n\t\tthrow (new Error('attempted deep copy of cyclic object'));\n\n\tif (obj && obj.constructor == Object) {\n\t\tret = {};\n\t\tobj[marker] = true;\n\n\t\tfor (key in obj) {\n\t\t\tif (key == marker)\n\t\t\t\tcontinue;\n\n\t\t\tret[key] = deepCopy(obj[key]);\n\t\t}\n\n\t\tdelete (obj[marker]);\n\t\treturn (ret);\n\t}\n\n\tif (obj && obj.constructor == Array) {\n\t\tret = [];\n\t\tobj[marker] = true;\n\n\t\tfor (key = 0; key < obj.length; key++)\n\t\t\tret.push(deepCopy(obj[key]));\n\n\t\tdelete (obj[marker]);\n\t\treturn (ret);\n\t}\n\n\t/*\n\t * It must be a primitive type -- just return it.\n\t */\n\treturn (obj);\n}\n\nfunction deepEqual(obj1, obj2)\n{\n\tif (typeof (obj1) != typeof (obj2))\n\t\treturn (false);\n\n\tif (obj1 === null || obj2 === null || typeof (obj1) != 'object')\n\t\treturn (obj1 === obj2);\n\n\tif (obj1.constructor != obj2.constructor)\n\t\treturn (false);\n\n\tvar k;\n\tfor (k in obj1) {\n\t\tif (!obj2.hasOwnProperty(k))\n\t\t\treturn (false);\n\n\t\tif (!deepEqual(obj1[k], obj2[k]))\n\t\t\treturn (false);\n\t}\n\n\tfor (k in obj2) {\n\t\tif (!obj1.hasOwnProperty(k))\n\t\t\treturn (false);\n\t}\n\n\treturn (true);\n}\n\nfunction isEmpty(obj)\n{\n\tvar key;\n\tfor (key in obj)\n\t\treturn (false);\n\treturn (true);\n}\n\nfunction hasKey(obj, key)\n{\n\tmod_assert.equal(typeof (key), 'string');\n\treturn (Object.prototype.hasOwnProperty.call(obj, key));\n}\n\nfunction forEachKey(obj, callback)\n{\n\tfor (var key in obj) {\n\t\tif (hasKey(obj, key)) {\n\t\t\tcallback(key, obj[key]);\n\t\t}\n\t}\n}\n\nfunction pluck(obj, key)\n{\n\tmod_assert.equal(typeof (key), 'string');\n\treturn (pluckv(obj, key));\n}\n\nfunction pluckv(obj, key)\n{\n\tif (obj === null || typeof (obj) !== 'object')\n\t\treturn (undefined);\n\n\tif (obj.hasOwnProperty(key))\n\t\treturn (obj[key]);\n\n\tvar i = key.indexOf('.');\n\tif (i == -1)\n\t\treturn (undefined);\n\n\tvar key1 = key.substr(0, i);\n\tif (!obj.hasOwnProperty(key1))\n\t\treturn (undefined);\n\n\treturn (pluckv(obj[key1], key.substr(i + 1)));\n}\n\n/*\n * Invoke callback(row) for each entry in the array that would be returned by\n * flattenObject(data, depth).  This is just like flattenObject(data,\n * depth).forEach(callback), except that the intermediate array is never\n * created.\n */\nfunction flattenIter(data, depth, callback)\n{\n\tdoFlattenIter(data, depth, [], callback);\n}\n\nfunction doFlattenIter(data, depth, accum, callback)\n{\n\tvar each;\n\tvar key;\n\n\tif (depth === 0) {\n\t\teach = accum.slice(0);\n\t\teach.push(data);\n\t\tcallback(each);\n\t\treturn;\n\t}\n\n\tmod_assert.ok(data !== null);\n\tmod_assert.equal(typeof (data), 'object');\n\tmod_assert.equal(typeof (depth), 'number');\n\tmod_assert.ok(depth >= 0);\n\n\tfor (key in data) {\n\t\teach = accum.slice(0);\n\t\teach.push(key);\n\t\tdoFlattenIter(data[key], depth - 1, each, callback);\n\t}\n}\n\nfunction flattenObject(data, depth)\n{\n\tif (depth === 0)\n\t\treturn ([ data ]);\n\n\tmod_assert.ok(data !== null);\n\tmod_assert.equal(typeof (data), 'object');\n\tmod_assert.equal(typeof (depth), 'number');\n\tmod_assert.ok(depth >= 0);\n\n\tvar rv = [];\n\tvar key;\n\n\tfor (key in data) {\n\t\tflattenObject(data[key], depth - 1).forEach(function (p) {\n\t\t\trv.push([ key ].concat(p));\n\t\t});\n\t}\n\n\treturn (rv);\n}\n\nfunction startsWith(str, prefix)\n{\n\treturn (str.substr(0, prefix.length) == prefix);\n}\n\nfunction endsWith(str, suffix)\n{\n\treturn (str.substr(\n\t    str.length - suffix.length, suffix.length) == suffix);\n}\n\nfunction iso8601(d)\n{\n\tif (typeof (d) == 'number')\n\t\td = new Date(d);\n\tmod_assert.ok(d.constructor === Date);\n\treturn (mod_extsprintf.sprintf('%4d-%02d-%02dT%02d:%02d:%02d.%03dZ',\n\t    d.getUTCFullYear(), d.getUTCMonth() + 1, d.getUTCDate(),\n\t    d.getUTCHours(), d.getUTCMinutes(), d.getUTCSeconds(),\n\t    d.getUTCMilliseconds()));\n}\n\nvar RFC1123_MONTHS = [\n    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\n    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\nvar RFC1123_DAYS = [\n    'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n\nfunction rfc1123(date) {\n\treturn (mod_extsprintf.sprintf('%s, %02d %s %04d %02d:%02d:%02d GMT',\n\t    RFC1123_DAYS[date.getUTCDay()], date.getUTCDate(),\n\t    RFC1123_MONTHS[date.getUTCMonth()], date.getUTCFullYear(),\n\t    date.getUTCHours(), date.getUTCMinutes(),\n\t    date.getUTCSeconds()));\n}\n\n/*\n * Parses a date expressed as a string, as either a number of milliseconds since\n * the epoch or any string format that Date accepts, giving preference to the\n * former where these two sets overlap (e.g., small numbers).\n */\nfunction parseDateTime(str)\n{\n\t/*\n\t * This is irritatingly implicit, but significantly more concise than\n\t * alternatives.  The \"+str\" will convert a string containing only a\n\t * number directly to a Number, or NaN for other strings.  Thus, if the\n\t * conversion succeeds, we use it (this is the milliseconds-since-epoch\n\t * case).  Otherwise, we pass the string directly to the Date\n\t * constructor to parse.\n\t */\n\tvar numeric = +str;\n\tif (!isNaN(numeric)) {\n\t\treturn (new Date(numeric));\n\t} else {\n\t\treturn (new Date(str));\n\t}\n}\n\n\n/*\n * Number.*_SAFE_INTEGER isn't present before node v0.12, so we hardcode\n * the ES6 definitions here, while allowing for them to someday be higher.\n */\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER || 9007199254740991;\nvar MIN_SAFE_INTEGER = Number.MIN_SAFE_INTEGER || -9007199254740991;\n\n\n/*\n * Default options for parseInteger().\n */\nvar PI_DEFAULTS = {\n\tbase: 10,\n\tallowSign: true,\n\tallowPrefix: false,\n\tallowTrailing: false,\n\tallowImprecise: false,\n\ttrimWhitespace: false,\n\tleadingZeroIsOctal: false\n};\n\nvar CP_0 = 0x30;\nvar CP_9 = 0x39;\n\nvar CP_A = 0x41;\nvar CP_B = 0x42;\nvar CP_O = 0x4f;\nvar CP_T = 0x54;\nvar CP_X = 0x58;\nvar CP_Z = 0x5a;\n\nvar CP_a = 0x61;\nvar CP_b = 0x62;\nvar CP_o = 0x6f;\nvar CP_t = 0x74;\nvar CP_x = 0x78;\nvar CP_z = 0x7a;\n\nvar PI_CONV_DEC = 0x30;\nvar PI_CONV_UC = 0x37;\nvar PI_CONV_LC = 0x57;\n\n\n/*\n * A stricter version of parseInt() that provides options for changing what\n * is an acceptable string (for example, disallowing trailing characters).\n */\nfunction parseInteger(str, uopts)\n{\n\tmod_assert.string(str, 'str');\n\tmod_assert.optionalObject(uopts, 'options');\n\n\tvar baseOverride = false;\n\tvar options = PI_DEFAULTS;\n\n\tif (uopts) {\n\t\tbaseOverride = hasKey(uopts, 'base');\n\t\toptions = mergeObjects(options, uopts);\n\t\tmod_assert.number(options.base, 'options.base');\n\t\tmod_assert.ok(options.base >= 2, 'options.base >= 2');\n\t\tmod_assert.ok(options.base <= 36, 'options.base <= 36');\n\t\tmod_assert.bool(options.allowSign, 'options.allowSign');\n\t\tmod_assert.bool(options.allowPrefix, 'options.allowPrefix');\n\t\tmod_assert.bool(options.allowTrailing,\n\t\t    'options.allowTrailing');\n\t\tmod_assert.bool(options.allowImprecise,\n\t\t    'options.allowImprecise');\n\t\tmod_assert.bool(options.trimWhitespace,\n\t\t    'options.trimWhitespace');\n\t\tmod_assert.bool(options.leadingZeroIsOctal,\n\t\t    'options.leadingZeroIsOctal');\n\n\t\tif (options.leadingZeroIsOctal) {\n\t\t\tmod_assert.ok(!baseOverride,\n\t\t\t    '\"base\" and \"leadingZeroIsOctal\" are ' +\n\t\t\t    'mutually exclusive');\n\t\t}\n\t}\n\n\tvar c;\n\tvar pbase = -1;\n\tvar base = options.base;\n\tvar start;\n\tvar mult = 1;\n\tvar value = 0;\n\tvar idx = 0;\n\tvar len = str.length;\n\n\t/* Trim any whitespace on the left side. */\n\tif (options.trimWhitespace) {\n\t\twhile (idx < len && isSpace(str.charCodeAt(idx))) {\n\t\t\t++idx;\n\t\t}\n\t}\n\n\t/* Check the number for a leading sign. */\n\tif (options.allowSign) {\n\t\tif (str[idx] === '-') {\n\t\t\tidx += 1;\n\t\t\tmult = -1;\n\t\t} else if (str[idx] === '+') {\n\t\t\tidx += 1;\n\t\t}\n\t}\n\n\t/* Parse the base-indicating prefix if there is one. */\n\tif (str[idx] === '0') {\n\t\tif (options.allowPrefix) {\n\t\t\tpbase = prefixToBase(str.charCodeAt(idx + 1));\n\t\t\tif (pbase !== -1 && (!baseOverride || pbase === base)) {\n\t\t\t\tbase = pbase;\n\t\t\t\tidx += 2;\n\t\t\t}\n\t\t}\n\n\t\tif (pbase === -1 && options.leadingZeroIsOctal) {\n\t\t\tbase = 8;\n\t\t}\n\t}\n\n\t/* Parse the actual digits. */\n\tfor (start = idx; idx < len; ++idx) {\n\t\tc = translateDigit(str.charCodeAt(idx));\n\t\tif (c !== -1 && c < base) {\n\t\t\tvalue *= base;\n\t\t\tvalue += c;\n\t\t} else {\n\t\t\tbreak;\n\t\t}\n\t}\n\n\t/* If we didn't parse any digits, we have an invalid number. */\n\tif (start === idx) {\n\t\treturn (new Error('invalid number: ' + JSON.stringify(str)));\n\t}\n\n\t/* Trim any whitespace on the right side. */\n\tif (options.trimWhitespace) {\n\t\twhile (idx < len && isSpace(str.charCodeAt(idx))) {\n\t\t\t++idx;\n\t\t}\n\t}\n\n\t/* Check for trailing characters. */\n\tif (idx < len && !options.allowTrailing) {\n\t\treturn (new Error('trailing characters after number: ' +\n\t\t    JSON.stringify(str.slice(idx))));\n\t}\n\n\t/* If our value is 0, we return now, to avoid returning -0. */\n\tif (value === 0) {\n\t\treturn (0);\n\t}\n\n\t/* Calculate our final value. */\n\tvar result = value * mult;\n\n\t/*\n\t * If the string represents a value that cannot be precisely represented\n\t * by JavaScript, then we want to check that:\n\t *\n\t * - We never increased the value past MAX_SAFE_INTEGER\n\t * - We don't make the result negative and below MIN_SAFE_INTEGER\n\t *\n\t * Because we only ever increment the value during parsing, there's no\n\t * chance of moving past MAX_SAFE_INTEGER and then dropping below it\n\t * again, losing precision in the process. This means that we only need\n\t * to do our checks here, at the end.\n\t */\n\tif (!options.allowImprecise &&\n\t    (value > MAX_SAFE_INTEGER || result < MIN_SAFE_INTEGER)) {\n\t\treturn (new Error('number is outside of the supported range: ' +\n\t\t    JSON.stringify(str.slice(start, idx))));\n\t}\n\n\treturn (result);\n}\n\n\n/*\n * Interpret a character code as a base-36 digit.\n */\nfunction translateDigit(d)\n{\n\tif (d >= CP_0 && d <= CP_9) {\n\t\t/* '0' to '9' -> 0 to 9 */\n\t\treturn (d - PI_CONV_DEC);\n\t} else if (d >= CP_A && d <= CP_Z) {\n\t\t/* 'A' - 'Z' -> 10 to 35 */\n\t\treturn (d - PI_CONV_UC);\n\t} else if (d >= CP_a && d <= CP_z) {\n\t\t/* 'a' - 'z' -> 10 to 35 */\n\t\treturn (d - PI_CONV_LC);\n\t} else {\n\t\t/* Invalid character code */\n\t\treturn (-1);\n\t}\n}\n\n\n/*\n * Test if a value matches the ECMAScript definition of trimmable whitespace.\n */\nfunction isSpace(c)\n{\n\treturn (c === 0x20) ||\n\t    (c >= 0x0009 && c <= 0x000d) ||\n\t    (c === 0x00a0) ||\n\t    (c === 0x1680) ||\n\t    (c === 0x180e) ||\n\t    (c >= 0x2000 && c <= 0x200a) ||\n\t    (c === 0x2028) ||\n\t    (c === 0x2029) ||\n\t    (c === 0x202f) ||\n\t    (c === 0x205f) ||\n\t    (c === 0x3000) ||\n\t    (c === 0xfeff);\n}\n\n\n/*\n * Determine which base a character indicates (e.g., 'x' indicates hex).\n */\nfunction prefixToBase(c)\n{\n\tif (c === CP_b || c === CP_B) {\n\t\t/* 0b/0B (binary) */\n\t\treturn (2);\n\t} else if (c === CP_o || c === CP_O) {\n\t\t/* 0o/0O (octal) */\n\t\treturn (8);\n\t} else if (c === CP_t || c === CP_T) {\n\t\t/* 0t/0T (decimal) */\n\t\treturn (10);\n\t} else if (c === CP_x || c === CP_X) {\n\t\t/* 0x/0X (hexadecimal) */\n\t\treturn (16);\n\t} else {\n\t\t/* Not a meaningful character */\n\t\treturn (-1);\n\t}\n}\n\n\nfunction validateJsonObjectJS(schema, input)\n{\n\tvar report = mod_jsonschema.validate(input, schema);\n\n\tif (report.errors.length === 0)\n\t\treturn (null);\n\n\t/* Currently, we only do anything useful with the first error. */\n\tvar error = report.errors[0];\n\n\t/* The failed property is given by a URI with an irrelevant prefix. */\n\tvar propname = error['property'];\n\tvar reason = error['message'].toLowerCase();\n\tvar i, j;\n\n\t/*\n\t * There's at least one case where the property error message is\n\t * confusing at best.  We work around this here.\n\t */\n\tif ((i = reason.indexOf('the property ')) != -1 &&\n\t    (j = reason.indexOf(' is not defined in the schema and the ' +\n\t    'schema does not allow additional properties')) != -1) {\n\t\ti += 'the property '.length;\n\t\tif (propname === '')\n\t\t\tpropname = reason.substr(i, j - i);\n\t\telse\n\t\t\tpropname = propname + '.' + reason.substr(i, j - i);\n\n\t\treason = 'unsupported property';\n\t}\n\n\tvar rv = new mod_verror.VError('property \"%s\": %s', propname, reason);\n\trv.jsv_details = error;\n\treturn (rv);\n}\n\nfunction randElt(arr)\n{\n\tmod_assert.ok(Array.isArray(arr) && arr.length > 0,\n\t    'randElt argument must be a non-empty array');\n\n\treturn (arr[Math.floor(Math.random() * arr.length)]);\n}\n\nfunction assertHrtime(a)\n{\n\tmod_assert.ok(a[0] >= 0 && a[1] >= 0,\n\t    'negative numbers not allowed in hrtimes');\n\tmod_assert.ok(a[1] < 1e9, 'nanoseconds column overflow');\n}\n\n/*\n * Compute the time elapsed between hrtime readings A and B, where A is later\n * than B.  hrtime readings come from Node's process.hrtime().  There is no\n * defined way to represent negative deltas, so it's illegal to diff B from A\n * where the time denoted by B is later than the time denoted by A.  If this\n * becomes valuable, we can define a representation and extend the\n * implementation to support it.\n */\nfunction hrtimeDiff(a, b)\n{\n\tassertHrtime(a);\n\tassertHrtime(b);\n\tmod_assert.ok(a[0] > b[0] || (a[0] == b[0] && a[1] >= b[1]),\n\t    'negative differences not allowed');\n\n\tvar rv = [ a[0] - b[0], 0 ];\n\n\tif (a[1] >= b[1]) {\n\t\trv[1] = a[1] - b[1];\n\t} else {\n\t\trv[0]--;\n\t\trv[1] = 1e9 - (b[1] - a[1]);\n\t}\n\n\treturn (rv);\n}\n\n/*\n * Convert a hrtime reading from the array format returned by Node's\n * process.hrtime() into a scalar number of nanoseconds.\n */\nfunction hrtimeNanosec(a)\n{\n\tassertHrtime(a);\n\n\treturn (Math.floor(a[0] * 1e9 + a[1]));\n}\n\n/*\n * Convert a hrtime reading from the array format returned by Node's\n * process.hrtime() into a scalar number of microseconds.\n */\nfunction hrtimeMicrosec(a)\n{\n\tassertHrtime(a);\n\n\treturn (Math.floor(a[0] * 1e6 + a[1] / 1e3));\n}\n\n/*\n * Convert a hrtime reading from the array format returned by Node's\n * process.hrtime() into a scalar number of milliseconds.\n */\nfunction hrtimeMillisec(a)\n{\n\tassertHrtime(a);\n\n\treturn (Math.floor(a[0] * 1e3 + a[1] / 1e6));\n}\n\n/*\n * Add two hrtime readings A and B, overwriting A with the result of the\n * addition.  This function is useful for accumulating several hrtime intervals\n * into a counter.  Returns A.\n */\nfunction hrtimeAccum(a, b)\n{\n\tassertHrtime(a);\n\tassertHrtime(b);\n\n\t/*\n\t * Accumulate the nanosecond component.\n\t */\n\ta[1] += b[1];\n\tif (a[1] >= 1e9) {\n\t\t/*\n\t\t * The nanosecond component overflowed, so carry to the seconds\n\t\t * field.\n\t\t */\n\t\ta[0]++;\n\t\ta[1] -= 1e9;\n\t}\n\n\t/*\n\t * Accumulate the seconds component.\n\t */\n\ta[0] += b[0];\n\n\treturn (a);\n}\n\n/*\n * Add two hrtime readings A and B, returning the result as a new hrtime array.\n * Does not modify either input argument.\n */\nfunction hrtimeAdd(a, b)\n{\n\tassertHrtime(a);\n\n\tvar rv = [ a[0], a[1] ];\n\n\treturn (hrtimeAccum(rv, b));\n}\n\n\n/*\n * Check an object for unexpected properties.  Accepts the object to check, and\n * an array of allowed property names (strings).  Returns an array of key names\n * that were found on the object, but did not appear in the list of allowed\n * properties.  If no properties were found, the returned array will be of\n * zero length.\n */\nfunction extraProperties(obj, allowed)\n{\n\tmod_assert.ok(typeof (obj) === 'object' && obj !== null,\n\t    'obj argument must be a non-null object');\n\tmod_assert.ok(Array.isArray(allowed),\n\t    'allowed argument must be an array of strings');\n\tfor (var i = 0; i < allowed.length; i++) {\n\t\tmod_assert.ok(typeof (allowed[i]) === 'string',\n\t\t    'allowed argument must be an array of strings');\n\t}\n\n\treturn (Object.keys(obj).filter(function (key) {\n\t\treturn (allowed.indexOf(key) === -1);\n\t}));\n}\n\n/*\n * Given three sets of properties \"provided\" (may be undefined), \"overrides\"\n * (required), and \"defaults\" (may be undefined), construct an object containing\n * the union of these sets with \"overrides\" overriding \"provided\", and\n * \"provided\" overriding \"defaults\".  None of the input objects are modified.\n */\nfunction mergeObjects(provided, overrides, defaults)\n{\n\tvar rv, k;\n\n\trv = {};\n\tif (defaults) {\n\t\tfor (k in defaults)\n\t\t\trv[k] = defaults[k];\n\t}\n\n\tif (provided) {\n\t\tfor (k in provided)\n\t\t\trv[k] = provided[k];\n\t}\n\n\tif (overrides) {\n\t\tfor (k in overrides)\n\t\t\trv[k] = overrides[k];\n\t}\n\n\treturn (rv);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsprim/lib/jsprim.js\n");

/***/ })

};
;