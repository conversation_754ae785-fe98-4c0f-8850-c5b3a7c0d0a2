/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth-sign";
exports.ids = ["vendor-chunks/oauth-sign"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth-sign/index.js":
/*!******************************************!*\
  !*** ./node_modules/oauth-sign/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto = __webpack_require__(/*! crypto */ \"crypto\")\n\nfunction sha (key, body, algorithm) {\n  return crypto.createHmac(algorithm, key).update(body).digest('base64')\n}\n\nfunction rsa (key, body) {\n  return crypto.createSign('RSA-SHA1').update(body).sign(key, 'base64')\n}\n\nfunction rfc3986 (str) {\n  return encodeURIComponent(str)\n    .replace(/!/g,'%21')\n    .replace(/\\*/g,'%2A')\n    .replace(/\\(/g,'%28')\n    .replace(/\\)/g,'%29')\n    .replace(/'/g,'%27')\n}\n\n// Maps object to bi-dimensional array\n// Converts { foo: 'A', bar: [ 'b', 'B' ]} to\n// [ ['foo', 'A'], ['bar', 'b'], ['bar', 'B'] ]\nfunction map (obj) {\n  var key, val, arr = []\n  for (key in obj) {\n    val = obj[key]\n    if (Array.isArray(val))\n      for (var i = 0; i < val.length; i++)\n        arr.push([key, val[i]])\n    else if (typeof val === 'object')\n      for (var prop in val)\n        arr.push([key + '[' + prop + ']', val[prop]])\n    else\n      arr.push([key, val])\n  }\n  return arr\n}\n\n// Compare function for sort\nfunction compare (a, b) {\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nfunction generateBase (httpMethod, base_uri, params) {\n  // adapted from https://dev.twitter.com/docs/auth/oauth and \n  // https://dev.twitter.com/docs/auth/creating-signature\n\n  // Parameter normalization\n  // http://tools.ietf.org/html/rfc5849#section-3.4.1.3.2\n  var normalized = map(params)\n  // 1.  First, the name and value of each parameter are encoded\n  .map(function (p) {\n    return [ rfc3986(p[0]), rfc3986(p[1] || '') ]\n  })\n  // 2.  The parameters are sorted by name, using ascending byte value\n  //     ordering.  If two or more parameters share the same name, they\n  //     are sorted by their value.\n  .sort(function (a, b) {\n    return compare(a[0], b[0]) || compare(a[1], b[1])\n  })\n  // 3.  The name of each parameter is concatenated to its corresponding\n  //     value using an \"=\" character (ASCII code 61) as a separator, even\n  //     if the value is empty.\n  .map(function (p) { return p.join('=') })\n   // 4.  The sorted name/value pairs are concatenated together into a\n   //     single string by using an \"&\" character (ASCII code 38) as\n   //     separator.\n  .join('&')\n\n  var base = [\n    rfc3986(httpMethod ? httpMethod.toUpperCase() : 'GET'),\n    rfc3986(base_uri),\n    rfc3986(normalized)\n  ].join('&')\n\n  return base\n}\n\nfunction hmacsign (httpMethod, base_uri, params, consumer_secret, token_secret) {\n  var base = generateBase(httpMethod, base_uri, params)\n  var key = [\n    consumer_secret || '',\n    token_secret || ''\n  ].map(rfc3986).join('&')\n\n  return sha(key, base, 'sha1')\n}\n\nfunction hmacsign256 (httpMethod, base_uri, params, consumer_secret, token_secret) {\n  var base = generateBase(httpMethod, base_uri, params)\n  var key = [\n    consumer_secret || '',\n    token_secret || ''\n  ].map(rfc3986).join('&')\n\n  return sha(key, base, 'sha256')\n}\n\nfunction rsasign (httpMethod, base_uri, params, private_key, token_secret) {\n  var base = generateBase(httpMethod, base_uri, params)\n  var key = private_key || ''\n\n  return rsa(key, base)\n}\n\nfunction plaintext (consumer_secret, token_secret) {\n  var key = [\n    consumer_secret || '',\n    token_secret || ''\n  ].map(rfc3986).join('&')\n\n  return key\n}\n\nfunction sign (signMethod, httpMethod, base_uri, params, consumer_secret, token_secret) {\n  var method\n  var skipArgs = 1\n\n  switch (signMethod) {\n    case 'RSA-SHA1':\n      method = rsasign\n      break\n    case 'HMAC-SHA1':\n      method = hmacsign\n      break\n    case 'HMAC-SHA256':\n      method = hmacsign256\n      break\n    case 'PLAINTEXT':\n      method = plaintext\n      skipArgs = 4\n      break\n    default:\n     throw new Error('Signature method not supported: ' + signMethod)\n  }\n\n  return method.apply(null, [].slice.call(arguments, skipArgs))\n}\n\nexports.hmacsign = hmacsign\nexports.hmacsign256 = hmacsign256\nexports.rsasign = rsasign\nexports.plaintext = plaintext\nexports.sign = sign\nexports.rfc3986 = rfc3986\nexports.generateBase = generateBase//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth-sign/index.js\n");

/***/ })

};
;