/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ecc-jsbn";
exports.ids = ["vendor-chunks/ecc-jsbn"];
exports.modules = {

/***/ "(rsc)/./node_modules/ecc-jsbn/index.js":
/*!****************************************!*\
  !*** ./node_modules/ecc-jsbn/index.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto = __webpack_require__(/*! crypto */ \"crypto\");\nvar BigInteger = (__webpack_require__(/*! jsbn */ \"(rsc)/./node_modules/jsbn/index.js\").BigInteger);\nvar ECPointFp = (__webpack_require__(/*! ./lib/ec.js */ \"(rsc)/./node_modules/ecc-jsbn/lib/ec.js\").ECPointFp);\nvar Buffer = (__webpack_require__(/*! safer-buffer */ \"(rsc)/./node_modules/safer-buffer/safer.js\").Buffer);\nexports.ECCurves = __webpack_require__(/*! ./lib/sec.js */ \"(rsc)/./node_modules/ecc-jsbn/lib/sec.js\");\n\n// zero prepad\nfunction unstupid(hex,len)\n{\n\treturn (hex.length >= len) ? hex : unstupid(\"0\"+hex,len);\n}\n\nexports.ECKey = function(curve, key, isPublic)\n{\n  var priv;\n\tvar c = curve();\n\tvar n = c.getN();\n  var bytes = Math.floor(n.bitLength()/8);\n\n  if(key)\n  {\n    if(isPublic)\n    {\n      var curve = c.getCurve();\n//      var x = key.slice(1,bytes+1); // skip the 04 for uncompressed format\n//      var y = key.slice(bytes+1);\n//      this.P = new ECPointFp(curve,\n//        curve.fromBigInteger(new BigInteger(x.toString(\"hex\"), 16)),\n//        curve.fromBigInteger(new BigInteger(y.toString(\"hex\"), 16)));      \n      this.P = curve.decodePointHex(key.toString(\"hex\"));\n    }else{\n      if(key.length != bytes) return false;\n      priv = new BigInteger(key.toString(\"hex\"), 16);      \n    }\n  }else{\n    var n1 = n.subtract(BigInteger.ONE);\n    var r = new BigInteger(crypto.randomBytes(n.bitLength()));\n    priv = r.mod(n1).add(BigInteger.ONE);\n    this.P = c.getG().multiply(priv);\n  }\n  if(this.P)\n  {\n//  var pubhex = unstupid(this.P.getX().toBigInteger().toString(16),bytes*2)+unstupid(this.P.getY().toBigInteger().toString(16),bytes*2);\n//  this.PublicKey = Buffer.from(\"04\"+pubhex,\"hex\");\n    this.PublicKey = Buffer.from(c.getCurve().encodeCompressedPointHex(this.P),\"hex\");\n  }\n  if(priv)\n  {\n    this.PrivateKey = Buffer.from(unstupid(priv.toString(16),bytes*2),\"hex\");\n    this.deriveSharedSecret = function(key)\n    {\n      if(!key || !key.P) return false;\n      var S = key.P.multiply(priv);\n      return Buffer.from(unstupid(S.getX().toBigInteger().toString(16),bytes*2),\"hex\");\n   }     \n  }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ecc-jsbn/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ecc-jsbn/lib/ec.js":
/*!*****************************************!*\
  !*** ./node_modules/ecc-jsbn/lib/ec.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basic Javascript Elliptic Curve implementation\n// Ported loosely from BouncyCastle's Java EC code\n// Only Fp curves implemented for now\n\n// Requires jsbn.js and jsbn2.js\nvar BigInteger = (__webpack_require__(/*! jsbn */ \"(rsc)/./node_modules/jsbn/index.js\").BigInteger)\nvar Barrett = BigInteger.prototype.Barrett\n\n// ----------------\n// ECFieldElementFp\n\n// constructor\nfunction ECFieldElementFp(q,x) {\n    this.x = x;\n    // TODO if(x.compareTo(q) >= 0) error\n    this.q = q;\n}\n\nfunction feFpEquals(other) {\n    if(other == this) return true;\n    return (this.q.equals(other.q) && this.x.equals(other.x));\n}\n\nfunction feFpToBigInteger() {\n    return this.x;\n}\n\nfunction feFpNegate() {\n    return new ECFieldElementFp(this.q, this.x.negate().mod(this.q));\n}\n\nfunction feFpAdd(b) {\n    return new ECFieldElementFp(this.q, this.x.add(b.toBigInteger()).mod(this.q));\n}\n\nfunction feFpSubtract(b) {\n    return new ECFieldElementFp(this.q, this.x.subtract(b.toBigInteger()).mod(this.q));\n}\n\nfunction feFpMultiply(b) {\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger()).mod(this.q));\n}\n\nfunction feFpSquare() {\n    return new ECFieldElementFp(this.q, this.x.square().mod(this.q));\n}\n\nfunction feFpDivide(b) {\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger().modInverse(this.q)).mod(this.q));\n}\n\nECFieldElementFp.prototype.equals = feFpEquals;\nECFieldElementFp.prototype.toBigInteger = feFpToBigInteger;\nECFieldElementFp.prototype.negate = feFpNegate;\nECFieldElementFp.prototype.add = feFpAdd;\nECFieldElementFp.prototype.subtract = feFpSubtract;\nECFieldElementFp.prototype.multiply = feFpMultiply;\nECFieldElementFp.prototype.square = feFpSquare;\nECFieldElementFp.prototype.divide = feFpDivide;\n\n// ----------------\n// ECPointFp\n\n// constructor\nfunction ECPointFp(curve,x,y,z) {\n    this.curve = curve;\n    this.x = x;\n    this.y = y;\n    // Projective coordinates: either zinv == null or z * zinv == 1\n    // z and zinv are just BigIntegers, not fieldElements\n    if(z == null) {\n      this.z = BigInteger.ONE;\n    }\n    else {\n      this.z = z;\n    }\n    this.zinv = null;\n    //TODO: compression flag\n}\n\nfunction pointFpGetX() {\n    if(this.zinv == null) {\n      this.zinv = this.z.modInverse(this.curve.q);\n    }\n    var r = this.x.toBigInteger().multiply(this.zinv);\n    this.curve.reduce(r);\n    return this.curve.fromBigInteger(r);\n}\n\nfunction pointFpGetY() {\n    if(this.zinv == null) {\n      this.zinv = this.z.modInverse(this.curve.q);\n    }\n    var r = this.y.toBigInteger().multiply(this.zinv);\n    this.curve.reduce(r);\n    return this.curve.fromBigInteger(r);\n}\n\nfunction pointFpEquals(other) {\n    if(other == this) return true;\n    if(this.isInfinity()) return other.isInfinity();\n    if(other.isInfinity()) return this.isInfinity();\n    var u, v;\n    // u = Y2 * Z1 - Y1 * Z2\n    u = other.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(other.z)).mod(this.curve.q);\n    if(!u.equals(BigInteger.ZERO)) return false;\n    // v = X2 * Z1 - X1 * Z2\n    v = other.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(other.z)).mod(this.curve.q);\n    return v.equals(BigInteger.ZERO);\n}\n\nfunction pointFpIsInfinity() {\n    if((this.x == null) && (this.y == null)) return true;\n    return this.z.equals(BigInteger.ZERO) && !this.y.toBigInteger().equals(BigInteger.ZERO);\n}\n\nfunction pointFpNegate() {\n    return new ECPointFp(this.curve, this.x, this.y.negate(), this.z);\n}\n\nfunction pointFpAdd(b) {\n    if(this.isInfinity()) return b;\n    if(b.isInfinity()) return this;\n\n    // u = Y2 * Z1 - Y1 * Z2\n    var u = b.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(b.z)).mod(this.curve.q);\n    // v = X2 * Z1 - X1 * Z2\n    var v = b.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(b.z)).mod(this.curve.q);\n\n    if(BigInteger.ZERO.equals(v)) {\n        if(BigInteger.ZERO.equals(u)) {\n            return this.twice(); // this == b, so double\n        }\n\treturn this.curve.getInfinity(); // this = -b, so infinity\n    }\n\n    var THREE = new BigInteger(\"3\");\n    var x1 = this.x.toBigInteger();\n    var y1 = this.y.toBigInteger();\n    var x2 = b.x.toBigInteger();\n    var y2 = b.y.toBigInteger();\n\n    var v2 = v.square();\n    var v3 = v2.multiply(v);\n    var x1v2 = x1.multiply(v2);\n    var zu2 = u.square().multiply(this.z);\n\n    // x3 = v * (z2 * (z1 * u^2 - 2 * x1 * v^2) - v^3)\n    var x3 = zu2.subtract(x1v2.shiftLeft(1)).multiply(b.z).subtract(v3).multiply(v).mod(this.curve.q);\n    // y3 = z2 * (3 * x1 * u * v^2 - y1 * v^3 - z1 * u^3) + u * v^3\n    var y3 = x1v2.multiply(THREE).multiply(u).subtract(y1.multiply(v3)).subtract(zu2.multiply(u)).multiply(b.z).add(u.multiply(v3)).mod(this.curve.q);\n    // z3 = v^3 * z1 * z2\n    var z3 = v3.multiply(this.z).multiply(b.z).mod(this.curve.q);\n\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3);\n}\n\nfunction pointFpTwice() {\n    if(this.isInfinity()) return this;\n    if(this.y.toBigInteger().signum() == 0) return this.curve.getInfinity();\n\n    // TODO: optimized handling of constants\n    var THREE = new BigInteger(\"3\");\n    var x1 = this.x.toBigInteger();\n    var y1 = this.y.toBigInteger();\n\n    var y1z1 = y1.multiply(this.z);\n    var y1sqz1 = y1z1.multiply(y1).mod(this.curve.q);\n    var a = this.curve.a.toBigInteger();\n\n    // w = 3 * x1^2 + a * z1^2\n    var w = x1.square().multiply(THREE);\n    if(!BigInteger.ZERO.equals(a)) {\n      w = w.add(this.z.square().multiply(a));\n    }\n    w = w.mod(this.curve.q);\n    //this.curve.reduce(w);\n    // x3 = 2 * y1 * z1 * (w^2 - 8 * x1 * y1^2 * z1)\n    var x3 = w.square().subtract(x1.shiftLeft(3).multiply(y1sqz1)).shiftLeft(1).multiply(y1z1).mod(this.curve.q);\n    // y3 = 4 * y1^2 * z1 * (3 * w * x1 - 2 * y1^2 * z1) - w^3\n    var y3 = w.multiply(THREE).multiply(x1).subtract(y1sqz1.shiftLeft(1)).shiftLeft(2).multiply(y1sqz1).subtract(w.square().multiply(w)).mod(this.curve.q);\n    // z3 = 8 * (y1 * z1)^3\n    var z3 = y1z1.square().multiply(y1z1).shiftLeft(3).mod(this.curve.q);\n\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3);\n}\n\n// Simple NAF (Non-Adjacent Form) multiplication algorithm\n// TODO: modularize the multiplication algorithm\nfunction pointFpMultiply(k) {\n    if(this.isInfinity()) return this;\n    if(k.signum() == 0) return this.curve.getInfinity();\n\n    var e = k;\n    var h = e.multiply(new BigInteger(\"3\"));\n\n    var neg = this.negate();\n    var R = this;\n\n    var i;\n    for(i = h.bitLength() - 2; i > 0; --i) {\n\tR = R.twice();\n\n\tvar hBit = h.testBit(i);\n\tvar eBit = e.testBit(i);\n\n\tif (hBit != eBit) {\n\t    R = R.add(hBit ? this : neg);\n\t}\n    }\n\n    return R;\n}\n\n// Compute this*j + x*k (simultaneous multiplication)\nfunction pointFpMultiplyTwo(j,x,k) {\n  var i;\n  if(j.bitLength() > k.bitLength())\n    i = j.bitLength() - 1;\n  else\n    i = k.bitLength() - 1;\n\n  var R = this.curve.getInfinity();\n  var both = this.add(x);\n  while(i >= 0) {\n    R = R.twice();\n    if(j.testBit(i)) {\n      if(k.testBit(i)) {\n        R = R.add(both);\n      }\n      else {\n        R = R.add(this);\n      }\n    }\n    else {\n      if(k.testBit(i)) {\n        R = R.add(x);\n      }\n    }\n    --i;\n  }\n\n  return R;\n}\n\nECPointFp.prototype.getX = pointFpGetX;\nECPointFp.prototype.getY = pointFpGetY;\nECPointFp.prototype.equals = pointFpEquals;\nECPointFp.prototype.isInfinity = pointFpIsInfinity;\nECPointFp.prototype.negate = pointFpNegate;\nECPointFp.prototype.add = pointFpAdd;\nECPointFp.prototype.twice = pointFpTwice;\nECPointFp.prototype.multiply = pointFpMultiply;\nECPointFp.prototype.multiplyTwo = pointFpMultiplyTwo;\n\n// ----------------\n// ECCurveFp\n\n// constructor\nfunction ECCurveFp(q,a,b) {\n    this.q = q;\n    this.a = this.fromBigInteger(a);\n    this.b = this.fromBigInteger(b);\n    this.infinity = new ECPointFp(this, null, null);\n    this.reducer = new Barrett(this.q);\n}\n\nfunction curveFpGetQ() {\n    return this.q;\n}\n\nfunction curveFpGetA() {\n    return this.a;\n}\n\nfunction curveFpGetB() {\n    return this.b;\n}\n\nfunction curveFpEquals(other) {\n    if(other == this) return true;\n    return(this.q.equals(other.q) && this.a.equals(other.a) && this.b.equals(other.b));\n}\n\nfunction curveFpGetInfinity() {\n    return this.infinity;\n}\n\nfunction curveFpFromBigInteger(x) {\n    return new ECFieldElementFp(this.q, x);\n}\n\nfunction curveReduce(x) {\n    this.reducer.reduce(x);\n}\n\n// for now, work with hex strings because they're easier in JS\nfunction curveFpDecodePointHex(s) {\n    switch(parseInt(s.substr(0,2), 16)) { // first byte\n    case 0:\n\treturn this.infinity;\n    case 2:\n    case 3:\n\t// point compression not supported yet\n\treturn null;\n    case 4:\n    case 6:\n    case 7:\n\tvar len = (s.length - 2) / 2;\n\tvar xHex = s.substr(2, len);\n\tvar yHex = s.substr(len+2, len);\n\n\treturn new ECPointFp(this,\n\t\t\t     this.fromBigInteger(new BigInteger(xHex, 16)),\n\t\t\t     this.fromBigInteger(new BigInteger(yHex, 16)));\n\n    default: // unsupported\n\treturn null;\n    }\n}\n\nfunction curveFpEncodePointHex(p) {\n\tif (p.isInfinity()) return \"00\";\n\tvar xHex = p.getX().toBigInteger().toString(16);\n\tvar yHex = p.getY().toBigInteger().toString(16);\n\tvar oLen = this.getQ().toString(16).length;\n\tif ((oLen % 2) != 0) oLen++;\n\twhile (xHex.length < oLen) {\n\t\txHex = \"0\" + xHex;\n\t}\n\twhile (yHex.length < oLen) {\n\t\tyHex = \"0\" + yHex;\n\t}\n\treturn \"04\" + xHex + yHex;\n}\n\nECCurveFp.prototype.getQ = curveFpGetQ;\nECCurveFp.prototype.getA = curveFpGetA;\nECCurveFp.prototype.getB = curveFpGetB;\nECCurveFp.prototype.equals = curveFpEquals;\nECCurveFp.prototype.getInfinity = curveFpGetInfinity;\nECCurveFp.prototype.fromBigInteger = curveFpFromBigInteger;\nECCurveFp.prototype.reduce = curveReduce;\n//ECCurveFp.prototype.decodePointHex = curveFpDecodePointHex;\nECCurveFp.prototype.encodePointHex = curveFpEncodePointHex;\n\n// from: https://github.com/kaielvin/jsbn-ec-point-compression\nECCurveFp.prototype.decodePointHex = function(s)\n{\n\tvar yIsEven;\n    switch(parseInt(s.substr(0,2), 16)) { // first byte\n    case 0:\n\treturn this.infinity;\n    case 2:\n\tyIsEven = false;\n    case 3:\n\tif(yIsEven == undefined) yIsEven = true;\n\tvar len = s.length - 2;\n\tvar xHex = s.substr(2, len);\n\tvar x = this.fromBigInteger(new BigInteger(xHex,16));\n\tvar alpha = x.multiply(x.square().add(this.getA())).add(this.getB());\n\tvar beta = alpha.sqrt();\n\n    if (beta == null) throw \"Invalid point compression\";\n\n    var betaValue = beta.toBigInteger();\n    if (betaValue.testBit(0) != yIsEven)\n    {\n        // Use the other root\n        beta = this.fromBigInteger(this.getQ().subtract(betaValue));\n    }\n    return new ECPointFp(this,x,beta);\n    case 4:\n    case 6:\n    case 7:\n\tvar len = (s.length - 2) / 2;\n\tvar xHex = s.substr(2, len);\n\tvar yHex = s.substr(len+2, len);\n\n\treturn new ECPointFp(this,\n\t\t\t     this.fromBigInteger(new BigInteger(xHex, 16)),\n\t\t\t     this.fromBigInteger(new BigInteger(yHex, 16)));\n\n    default: // unsupported\n\treturn null;\n    }\n}\nECCurveFp.prototype.encodeCompressedPointHex = function(p)\n{\n\tif (p.isInfinity()) return \"00\";\n\tvar xHex = p.getX().toBigInteger().toString(16);\n\tvar oLen = this.getQ().toString(16).length;\n\tif ((oLen % 2) != 0) oLen++;\n\twhile (xHex.length < oLen)\n\t\txHex = \"0\" + xHex;\n\tvar yPrefix;\n\tif(p.getY().toBigInteger().isEven()) yPrefix = \"02\";\n\telse                                 yPrefix = \"03\";\n\n\treturn yPrefix + xHex;\n}\n\n\nECFieldElementFp.prototype.getR = function()\n{\n\tif(this.r != undefined) return this.r;\n\n    this.r = null;\n    var bitLength = this.q.bitLength();\n    if (bitLength > 128)\n    {\n        var firstWord = this.q.shiftRight(bitLength - 64);\n        if (firstWord.intValue() == -1)\n        {\n            this.r = BigInteger.ONE.shiftLeft(bitLength).subtract(this.q);\n        }\n    }\n    return this.r;\n}\nECFieldElementFp.prototype.modMult = function(x1,x2)\n{\n    return this.modReduce(x1.multiply(x2));\n}\nECFieldElementFp.prototype.modReduce = function(x)\n{\n    if (this.getR() != null)\n    {\n        var qLen = q.bitLength();\n        while (x.bitLength() > (qLen + 1))\n        {\n            var u = x.shiftRight(qLen);\n            var v = x.subtract(u.shiftLeft(qLen));\n            if (!this.getR().equals(BigInteger.ONE))\n            {\n                u = u.multiply(this.getR());\n            }\n            x = u.add(v); \n        }\n        while (x.compareTo(q) >= 0)\n        {\n            x = x.subtract(q);\n        }\n    }\n    else\n    {\n        x = x.mod(q);\n    }\n    return x;\n}\nECFieldElementFp.prototype.sqrt = function()\n{\n    if (!this.q.testBit(0)) throw \"unsupported\";\n\n    // p mod 4 == 3\n    if (this.q.testBit(1))\n    {\n    \tvar z = new ECFieldElementFp(this.q,this.x.modPow(this.q.shiftRight(2).add(BigInteger.ONE),this.q));\n    \treturn z.square().equals(this) ? z : null;\n    }\n\n    // p mod 4 == 1\n    var qMinusOne = this.q.subtract(BigInteger.ONE);\n\n    var legendreExponent = qMinusOne.shiftRight(1);\n    if (!(this.x.modPow(legendreExponent, this.q).equals(BigInteger.ONE)))\n    {\n        return null;\n    }\n\n    var u = qMinusOne.shiftRight(2);\n    var k = u.shiftLeft(1).add(BigInteger.ONE);\n\n    var Q = this.x;\n    var fourQ = modDouble(modDouble(Q));\n\n    var U, V;\n    do\n    {\n        var P;\n        do\n        {\n            P = new BigInteger(this.q.bitLength(), new SecureRandom());\n        }\n        while (P.compareTo(this.q) >= 0\n            || !(P.multiply(P).subtract(fourQ).modPow(legendreExponent, this.q).equals(qMinusOne)));\n\n        var result = this.lucasSequence(P, Q, k);\n        U = result[0];\n        V = result[1];\n\n        if (this.modMult(V, V).equals(fourQ))\n        {\n            // Integer division by 2, mod q\n            if (V.testBit(0))\n            {\n                V = V.add(q);\n            }\n\n            V = V.shiftRight(1);\n\n            return new ECFieldElementFp(q,V);\n        }\n    }\n    while (U.equals(BigInteger.ONE) || U.equals(qMinusOne));\n\n    return null;\n}\nECFieldElementFp.prototype.lucasSequence = function(P,Q,k)\n{\n    var n = k.bitLength();\n    var s = k.getLowestSetBit();\n\n    var Uh = BigInteger.ONE;\n    var Vl = BigInteger.TWO;\n    var Vh = P;\n    var Ql = BigInteger.ONE;\n    var Qh = BigInteger.ONE;\n\n    for (var j = n - 1; j >= s + 1; --j)\n    {\n        Ql = this.modMult(Ql, Qh);\n\n        if (k.testBit(j))\n        {\n            Qh = this.modMult(Ql, Q);\n            Uh = this.modMult(Uh, Vh);\n            Vl = this.modReduce(Vh.multiply(Vl).subtract(P.multiply(Ql)));\n            Vh = this.modReduce(Vh.multiply(Vh).subtract(Qh.shiftLeft(1)));\n        }\n        else\n        {\n            Qh = Ql;\n            Uh = this.modReduce(Uh.multiply(Vl).subtract(Ql));\n            Vh = this.modReduce(Vh.multiply(Vl).subtract(P.multiply(Ql)));\n            Vl = this.modReduce(Vl.multiply(Vl).subtract(Ql.shiftLeft(1)));\n        }\n    }\n\n    Ql = this.modMult(Ql, Qh);\n    Qh = this.modMult(Ql, Q);\n    Uh = this.modReduce(Uh.multiply(Vl).subtract(Ql));\n    Vl = this.modReduce(Vh.multiply(Vl).subtract(P.multiply(Ql)));\n    Ql = this.modMult(Ql, Qh);\n\n    for (var j = 1; j <= s; ++j)\n    {\n        Uh = this.modMult(Uh, Vl);\n        Vl = this.modReduce(Vl.multiply(Vl).subtract(Ql.shiftLeft(1)));\n        Ql = this.modMult(Ql, Ql);\n    }\n\n    return [ Uh, Vl ];\n}\n\nvar exports = {\n  ECCurveFp: ECCurveFp,\n  ECPointFp: ECPointFp,\n  ECFieldElementFp: ECFieldElementFp\n}\n\nmodule.exports = exports\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZWNjLWpzYm4vbGliL2VjLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGlCQUFpQixrRkFBMEI7QUFDM0M7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBLGtDQUFrQztBQUNsQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLCtCQUErQixPQUFPO0FBQ3RDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDBDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx3QkFBd0IsWUFBWTtBQUNwQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9CQUFvQixRQUFRO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmF6b3JwYXktbmV4dC8uL25vZGVfbW9kdWxlcy9lY2MtanNibi9saWIvZWMuanM/YTdlNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBCYXNpYyBKYXZhc2NyaXB0IEVsbGlwdGljIEN1cnZlIGltcGxlbWVudGF0aW9uXG4vLyBQb3J0ZWQgbG9vc2VseSBmcm9tIEJvdW5jeUNhc3RsZSdzIEphdmEgRUMgY29kZVxuLy8gT25seSBGcCBjdXJ2ZXMgaW1wbGVtZW50ZWQgZm9yIG5vd1xuXG4vLyBSZXF1aXJlcyBqc2JuLmpzIGFuZCBqc2JuMi5qc1xudmFyIEJpZ0ludGVnZXIgPSByZXF1aXJlKCdqc2JuJykuQmlnSW50ZWdlclxudmFyIEJhcnJldHQgPSBCaWdJbnRlZ2VyLnByb3RvdHlwZS5CYXJyZXR0XG5cbi8vIC0tLS0tLS0tLS0tLS0tLS1cbi8vIEVDRmllbGRFbGVtZW50RnBcblxuLy8gY29uc3RydWN0b3JcbmZ1bmN0aW9uIEVDRmllbGRFbGVtZW50RnAocSx4KSB7XG4gICAgdGhpcy54ID0geDtcbiAgICAvLyBUT0RPIGlmKHguY29tcGFyZVRvKHEpID49IDApIGVycm9yXG4gICAgdGhpcy5xID0gcTtcbn1cblxuZnVuY3Rpb24gZmVGcEVxdWFscyhvdGhlcikge1xuICAgIGlmKG90aGVyID09IHRoaXMpIHJldHVybiB0cnVlO1xuICAgIHJldHVybiAodGhpcy5xLmVxdWFscyhvdGhlci5xKSAmJiB0aGlzLnguZXF1YWxzKG90aGVyLngpKTtcbn1cblxuZnVuY3Rpb24gZmVGcFRvQmlnSW50ZWdlcigpIHtcbiAgICByZXR1cm4gdGhpcy54O1xufVxuXG5mdW5jdGlvbiBmZUZwTmVnYXRlKCkge1xuICAgIHJldHVybiBuZXcgRUNGaWVsZEVsZW1lbnRGcCh0aGlzLnEsIHRoaXMueC5uZWdhdGUoKS5tb2QodGhpcy5xKSk7XG59XG5cbmZ1bmN0aW9uIGZlRnBBZGQoYikge1xuICAgIHJldHVybiBuZXcgRUNGaWVsZEVsZW1lbnRGcCh0aGlzLnEsIHRoaXMueC5hZGQoYi50b0JpZ0ludGVnZXIoKSkubW9kKHRoaXMucSkpO1xufVxuXG5mdW5jdGlvbiBmZUZwU3VidHJhY3QoYikge1xuICAgIHJldHVybiBuZXcgRUNGaWVsZEVsZW1lbnRGcCh0aGlzLnEsIHRoaXMueC5zdWJ0cmFjdChiLnRvQmlnSW50ZWdlcigpKS5tb2QodGhpcy5xKSk7XG59XG5cbmZ1bmN0aW9uIGZlRnBNdWx0aXBseShiKSB7XG4gICAgcmV0dXJuIG5ldyBFQ0ZpZWxkRWxlbWVudEZwKHRoaXMucSwgdGhpcy54Lm11bHRpcGx5KGIudG9CaWdJbnRlZ2VyKCkpLm1vZCh0aGlzLnEpKTtcbn1cblxuZnVuY3Rpb24gZmVGcFNxdWFyZSgpIHtcbiAgICByZXR1cm4gbmV3IEVDRmllbGRFbGVtZW50RnAodGhpcy5xLCB0aGlzLnguc3F1YXJlKCkubW9kKHRoaXMucSkpO1xufVxuXG5mdW5jdGlvbiBmZUZwRGl2aWRlKGIpIHtcbiAgICByZXR1cm4gbmV3IEVDRmllbGRFbGVtZW50RnAodGhpcy5xLCB0aGlzLngubXVsdGlwbHkoYi50b0JpZ0ludGVnZXIoKS5tb2RJbnZlcnNlKHRoaXMucSkpLm1vZCh0aGlzLnEpKTtcbn1cblxuRUNGaWVsZEVsZW1lbnRGcC5wcm90b3R5cGUuZXF1YWxzID0gZmVGcEVxdWFscztcbkVDRmllbGRFbGVtZW50RnAucHJvdG90eXBlLnRvQmlnSW50ZWdlciA9IGZlRnBUb0JpZ0ludGVnZXI7XG5FQ0ZpZWxkRWxlbWVudEZwLnByb3RvdHlwZS5uZWdhdGUgPSBmZUZwTmVnYXRlO1xuRUNGaWVsZEVsZW1lbnRGcC5wcm90b3R5cGUuYWRkID0gZmVGcEFkZDtcbkVDRmllbGRFbGVtZW50RnAucHJvdG90eXBlLnN1YnRyYWN0ID0gZmVGcFN1YnRyYWN0O1xuRUNGaWVsZEVsZW1lbnRGcC5wcm90b3R5cGUubXVsdGlwbHkgPSBmZUZwTXVsdGlwbHk7XG5FQ0ZpZWxkRWxlbWVudEZwLnByb3RvdHlwZS5zcXVhcmUgPSBmZUZwU3F1YXJlO1xuRUNGaWVsZEVsZW1lbnRGcC5wcm90b3R5cGUuZGl2aWRlID0gZmVGcERpdmlkZTtcblxuLy8gLS0tLS0tLS0tLS0tLS0tLVxuLy8gRUNQb2ludEZwXG5cbi8vIGNvbnN0cnVjdG9yXG5mdW5jdGlvbiBFQ1BvaW50RnAoY3VydmUseCx5LHopIHtcbiAgICB0aGlzLmN1cnZlID0gY3VydmU7XG4gICAgdGhpcy54ID0geDtcbiAgICB0aGlzLnkgPSB5O1xuICAgIC8vIFByb2plY3RpdmUgY29vcmRpbmF0ZXM6IGVpdGhlciB6aW52ID09IG51bGwgb3IgeiAqIHppbnYgPT0gMVxuICAgIC8vIHogYW5kIHppbnYgYXJlIGp1c3QgQmlnSW50ZWdlcnMsIG5vdCBmaWVsZEVsZW1lbnRzXG4gICAgaWYoeiA9PSBudWxsKSB7XG4gICAgICB0aGlzLnogPSBCaWdJbnRlZ2VyLk9ORTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICB0aGlzLnogPSB6O1xuICAgIH1cbiAgICB0aGlzLnppbnYgPSBudWxsO1xuICAgIC8vVE9ETzogY29tcHJlc3Npb24gZmxhZ1xufVxuXG5mdW5jdGlvbiBwb2ludEZwR2V0WCgpIHtcbiAgICBpZih0aGlzLnppbnYgPT0gbnVsbCkge1xuICAgICAgdGhpcy56aW52ID0gdGhpcy56Lm1vZEludmVyc2UodGhpcy5jdXJ2ZS5xKTtcbiAgICB9XG4gICAgdmFyIHIgPSB0aGlzLngudG9CaWdJbnRlZ2VyKCkubXVsdGlwbHkodGhpcy56aW52KTtcbiAgICB0aGlzLmN1cnZlLnJlZHVjZShyKTtcbiAgICByZXR1cm4gdGhpcy5jdXJ2ZS5mcm9tQmlnSW50ZWdlcihyKTtcbn1cblxuZnVuY3Rpb24gcG9pbnRGcEdldFkoKSB7XG4gICAgaWYodGhpcy56aW52ID09IG51bGwpIHtcbiAgICAgIHRoaXMuemludiA9IHRoaXMuei5tb2RJbnZlcnNlKHRoaXMuY3VydmUucSk7XG4gICAgfVxuICAgIHZhciByID0gdGhpcy55LnRvQmlnSW50ZWdlcigpLm11bHRpcGx5KHRoaXMuemludik7XG4gICAgdGhpcy5jdXJ2ZS5yZWR1Y2Uocik7XG4gICAgcmV0dXJuIHRoaXMuY3VydmUuZnJvbUJpZ0ludGVnZXIocik7XG59XG5cbmZ1bmN0aW9uIHBvaW50RnBFcXVhbHMob3RoZXIpIHtcbiAgICBpZihvdGhlciA9PSB0aGlzKSByZXR1cm4gdHJ1ZTtcbiAgICBpZih0aGlzLmlzSW5maW5pdHkoKSkgcmV0dXJuIG90aGVyLmlzSW5maW5pdHkoKTtcbiAgICBpZihvdGhlci5pc0luZmluaXR5KCkpIHJldHVybiB0aGlzLmlzSW5maW5pdHkoKTtcbiAgICB2YXIgdSwgdjtcbiAgICAvLyB1ID0gWTIgKiBaMSAtIFkxICogWjJcbiAgICB1ID0gb3RoZXIueS50b0JpZ0ludGVnZXIoKS5tdWx0aXBseSh0aGlzLnopLnN1YnRyYWN0KHRoaXMueS50b0JpZ0ludGVnZXIoKS5tdWx0aXBseShvdGhlci56KSkubW9kKHRoaXMuY3VydmUucSk7XG4gICAgaWYoIXUuZXF1YWxzKEJpZ0ludGVnZXIuWkVSTykpIHJldHVybiBmYWxzZTtcbiAgICAvLyB2ID0gWDIgKiBaMSAtIFgxICogWjJcbiAgICB2ID0gb3RoZXIueC50b0JpZ0ludGVnZXIoKS5tdWx0aXBseSh0aGlzLnopLnN1YnRyYWN0KHRoaXMueC50b0JpZ0ludGVnZXIoKS5tdWx0aXBseShvdGhlci56KSkubW9kKHRoaXMuY3VydmUucSk7XG4gICAgcmV0dXJuIHYuZXF1YWxzKEJpZ0ludGVnZXIuWkVSTyk7XG59XG5cbmZ1bmN0aW9uIHBvaW50RnBJc0luZmluaXR5KCkge1xuICAgIGlmKCh0aGlzLnggPT0gbnVsbCkgJiYgKHRoaXMueSA9PSBudWxsKSkgcmV0dXJuIHRydWU7XG4gICAgcmV0dXJuIHRoaXMuei5lcXVhbHMoQmlnSW50ZWdlci5aRVJPKSAmJiAhdGhpcy55LnRvQmlnSW50ZWdlcigpLmVxdWFscyhCaWdJbnRlZ2VyLlpFUk8pO1xufVxuXG5mdW5jdGlvbiBwb2ludEZwTmVnYXRlKCkge1xuICAgIHJldHVybiBuZXcgRUNQb2ludEZwKHRoaXMuY3VydmUsIHRoaXMueCwgdGhpcy55Lm5lZ2F0ZSgpLCB0aGlzLnopO1xufVxuXG5mdW5jdGlvbiBwb2ludEZwQWRkKGIpIHtcbiAgICBpZih0aGlzLmlzSW5maW5pdHkoKSkgcmV0dXJuIGI7XG4gICAgaWYoYi5pc0luZmluaXR5KCkpIHJldHVybiB0aGlzO1xuXG4gICAgLy8gdSA9IFkyICogWjEgLSBZMSAqIFoyXG4gICAgdmFyIHUgPSBiLnkudG9CaWdJbnRlZ2VyKCkubXVsdGlwbHkodGhpcy56KS5zdWJ0cmFjdCh0aGlzLnkudG9CaWdJbnRlZ2VyKCkubXVsdGlwbHkoYi56KSkubW9kKHRoaXMuY3VydmUucSk7XG4gICAgLy8gdiA9IFgyICogWjEgLSBYMSAqIFoyXG4gICAgdmFyIHYgPSBiLngudG9CaWdJbnRlZ2VyKCkubXVsdGlwbHkodGhpcy56KS5zdWJ0cmFjdCh0aGlzLngudG9CaWdJbnRlZ2VyKCkubXVsdGlwbHkoYi56KSkubW9kKHRoaXMuY3VydmUucSk7XG5cbiAgICBpZihCaWdJbnRlZ2VyLlpFUk8uZXF1YWxzKHYpKSB7XG4gICAgICAgIGlmKEJpZ0ludGVnZXIuWkVSTy5lcXVhbHModSkpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnR3aWNlKCk7IC8vIHRoaXMgPT0gYiwgc28gZG91YmxlXG4gICAgICAgIH1cblx0cmV0dXJuIHRoaXMuY3VydmUuZ2V0SW5maW5pdHkoKTsgLy8gdGhpcyA9IC1iLCBzbyBpbmZpbml0eVxuICAgIH1cblxuICAgIHZhciBUSFJFRSA9IG5ldyBCaWdJbnRlZ2VyKFwiM1wiKTtcbiAgICB2YXIgeDEgPSB0aGlzLngudG9CaWdJbnRlZ2VyKCk7XG4gICAgdmFyIHkxID0gdGhpcy55LnRvQmlnSW50ZWdlcigpO1xuICAgIHZhciB4MiA9IGIueC50b0JpZ0ludGVnZXIoKTtcbiAgICB2YXIgeTIgPSBiLnkudG9CaWdJbnRlZ2VyKCk7XG5cbiAgICB2YXIgdjIgPSB2LnNxdWFyZSgpO1xuICAgIHZhciB2MyA9IHYyLm11bHRpcGx5KHYpO1xuICAgIHZhciB4MXYyID0geDEubXVsdGlwbHkodjIpO1xuICAgIHZhciB6dTIgPSB1LnNxdWFyZSgpLm11bHRpcGx5KHRoaXMueik7XG5cbiAgICAvLyB4MyA9IHYgKiAoejIgKiAoejEgKiB1XjIgLSAyICogeDEgKiB2XjIpIC0gdl4zKVxuICAgIHZhciB4MyA9IHp1Mi5zdWJ0cmFjdCh4MXYyLnNoaWZ0TGVmdCgxKSkubXVsdGlwbHkoYi56KS5zdWJ0cmFjdCh2MykubXVsdGlwbHkodikubW9kKHRoaXMuY3VydmUucSk7XG4gICAgLy8geTMgPSB6MiAqICgzICogeDEgKiB1ICogdl4yIC0geTEgKiB2XjMgLSB6MSAqIHVeMykgKyB1ICogdl4zXG4gICAgdmFyIHkzID0geDF2Mi5tdWx0aXBseShUSFJFRSkubXVsdGlwbHkodSkuc3VidHJhY3QoeTEubXVsdGlwbHkodjMpKS5zdWJ0cmFjdCh6dTIubXVsdGlwbHkodSkpLm11bHRpcGx5KGIueikuYWRkKHUubXVsdGlwbHkodjMpKS5tb2QodGhpcy5jdXJ2ZS5xKTtcbiAgICAvLyB6MyA9IHZeMyAqIHoxICogejJcbiAgICB2YXIgejMgPSB2My5tdWx0aXBseSh0aGlzLnopLm11bHRpcGx5KGIueikubW9kKHRoaXMuY3VydmUucSk7XG5cbiAgICByZXR1cm4gbmV3IEVDUG9pbnRGcCh0aGlzLmN1cnZlLCB0aGlzLmN1cnZlLmZyb21CaWdJbnRlZ2VyKHgzKSwgdGhpcy5jdXJ2ZS5mcm9tQmlnSW50ZWdlcih5MyksIHozKTtcbn1cblxuZnVuY3Rpb24gcG9pbnRGcFR3aWNlKCkge1xuICAgIGlmKHRoaXMuaXNJbmZpbml0eSgpKSByZXR1cm4gdGhpcztcbiAgICBpZih0aGlzLnkudG9CaWdJbnRlZ2VyKCkuc2lnbnVtKCkgPT0gMCkgcmV0dXJuIHRoaXMuY3VydmUuZ2V0SW5maW5pdHkoKTtcblxuICAgIC8vIFRPRE86IG9wdGltaXplZCBoYW5kbGluZyBvZiBjb25zdGFudHNcbiAgICB2YXIgVEhSRUUgPSBuZXcgQmlnSW50ZWdlcihcIjNcIik7XG4gICAgdmFyIHgxID0gdGhpcy54LnRvQmlnSW50ZWdlcigpO1xuICAgIHZhciB5MSA9IHRoaXMueS50b0JpZ0ludGVnZXIoKTtcblxuICAgIHZhciB5MXoxID0geTEubXVsdGlwbHkodGhpcy56KTtcbiAgICB2YXIgeTFzcXoxID0geTF6MS5tdWx0aXBseSh5MSkubW9kKHRoaXMuY3VydmUucSk7XG4gICAgdmFyIGEgPSB0aGlzLmN1cnZlLmEudG9CaWdJbnRlZ2VyKCk7XG5cbiAgICAvLyB3ID0gMyAqIHgxXjIgKyBhICogejFeMlxuICAgIHZhciB3ID0geDEuc3F1YXJlKCkubXVsdGlwbHkoVEhSRUUpO1xuICAgIGlmKCFCaWdJbnRlZ2VyLlpFUk8uZXF1YWxzKGEpKSB7XG4gICAgICB3ID0gdy5hZGQodGhpcy56LnNxdWFyZSgpLm11bHRpcGx5KGEpKTtcbiAgICB9XG4gICAgdyA9IHcubW9kKHRoaXMuY3VydmUucSk7XG4gICAgLy90aGlzLmN1cnZlLnJlZHVjZSh3KTtcbiAgICAvLyB4MyA9IDIgKiB5MSAqIHoxICogKHdeMiAtIDggKiB4MSAqIHkxXjIgKiB6MSlcbiAgICB2YXIgeDMgPSB3LnNxdWFyZSgpLnN1YnRyYWN0KHgxLnNoaWZ0TGVmdCgzKS5tdWx0aXBseSh5MXNxejEpKS5zaGlmdExlZnQoMSkubXVsdGlwbHkoeTF6MSkubW9kKHRoaXMuY3VydmUucSk7XG4gICAgLy8geTMgPSA0ICogeTFeMiAqIHoxICogKDMgKiB3ICogeDEgLSAyICogeTFeMiAqIHoxKSAtIHdeM1xuICAgIHZhciB5MyA9IHcubXVsdGlwbHkoVEhSRUUpLm11bHRpcGx5KHgxKS5zdWJ0cmFjdCh5MXNxejEuc2hpZnRMZWZ0KDEpKS5zaGlmdExlZnQoMikubXVsdGlwbHkoeTFzcXoxKS5zdWJ0cmFjdCh3LnNxdWFyZSgpLm11bHRpcGx5KHcpKS5tb2QodGhpcy5jdXJ2ZS5xKTtcbiAgICAvLyB6MyA9IDggKiAoeTEgKiB6MSleM1xuICAgIHZhciB6MyA9IHkxejEuc3F1YXJlKCkubXVsdGlwbHkoeTF6MSkuc2hpZnRMZWZ0KDMpLm1vZCh0aGlzLmN1cnZlLnEpO1xuXG4gICAgcmV0dXJuIG5ldyBFQ1BvaW50RnAodGhpcy5jdXJ2ZSwgdGhpcy5jdXJ2ZS5mcm9tQmlnSW50ZWdlcih4MyksIHRoaXMuY3VydmUuZnJvbUJpZ0ludGVnZXIoeTMpLCB6Myk7XG59XG5cbi8vIFNpbXBsZSBOQUYgKE5vbi1BZGphY2VudCBGb3JtKSBtdWx0aXBsaWNhdGlvbiBhbGdvcml0aG1cbi8vIFRPRE86IG1vZHVsYXJpemUgdGhlIG11bHRpcGxpY2F0aW9uIGFsZ29yaXRobVxuZnVuY3Rpb24gcG9pbnRGcE11bHRpcGx5KGspIHtcbiAgICBpZih0aGlzLmlzSW5maW5pdHkoKSkgcmV0dXJuIHRoaXM7XG4gICAgaWYoay5zaWdudW0oKSA9PSAwKSByZXR1cm4gdGhpcy5jdXJ2ZS5nZXRJbmZpbml0eSgpO1xuXG4gICAgdmFyIGUgPSBrO1xuICAgIHZhciBoID0gZS5tdWx0aXBseShuZXcgQmlnSW50ZWdlcihcIjNcIikpO1xuXG4gICAgdmFyIG5lZyA9IHRoaXMubmVnYXRlKCk7XG4gICAgdmFyIFIgPSB0aGlzO1xuXG4gICAgdmFyIGk7XG4gICAgZm9yKGkgPSBoLmJpdExlbmd0aCgpIC0gMjsgaSA+IDA7IC0taSkge1xuXHRSID0gUi50d2ljZSgpO1xuXG5cdHZhciBoQml0ID0gaC50ZXN0Qml0KGkpO1xuXHR2YXIgZUJpdCA9IGUudGVzdEJpdChpKTtcblxuXHRpZiAoaEJpdCAhPSBlQml0KSB7XG5cdCAgICBSID0gUi5hZGQoaEJpdCA/IHRoaXMgOiBuZWcpO1xuXHR9XG4gICAgfVxuXG4gICAgcmV0dXJuIFI7XG59XG5cbi8vIENvbXB1dGUgdGhpcypqICsgeCprIChzaW11bHRhbmVvdXMgbXVsdGlwbGljYXRpb24pXG5mdW5jdGlvbiBwb2ludEZwTXVsdGlwbHlUd28oaix4LGspIHtcbiAgdmFyIGk7XG4gIGlmKGouYml0TGVuZ3RoKCkgPiBrLmJpdExlbmd0aCgpKVxuICAgIGkgPSBqLmJpdExlbmd0aCgpIC0gMTtcbiAgZWxzZVxuICAgIGkgPSBrLmJpdExlbmd0aCgpIC0gMTtcblxuICB2YXIgUiA9IHRoaXMuY3VydmUuZ2V0SW5maW5pdHkoKTtcbiAgdmFyIGJvdGggPSB0aGlzLmFkZCh4KTtcbiAgd2hpbGUoaSA+PSAwKSB7XG4gICAgUiA9IFIudHdpY2UoKTtcbiAgICBpZihqLnRlc3RCaXQoaSkpIHtcbiAgICAgIGlmKGsudGVzdEJpdChpKSkge1xuICAgICAgICBSID0gUi5hZGQoYm90aCk7XG4gICAgICB9XG4gICAgICBlbHNlIHtcbiAgICAgICAgUiA9IFIuYWRkKHRoaXMpO1xuICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgIGlmKGsudGVzdEJpdChpKSkge1xuICAgICAgICBSID0gUi5hZGQoeCk7XG4gICAgICB9XG4gICAgfVxuICAgIC0taTtcbiAgfVxuXG4gIHJldHVybiBSO1xufVxuXG5FQ1BvaW50RnAucHJvdG90eXBlLmdldFggPSBwb2ludEZwR2V0WDtcbkVDUG9pbnRGcC5wcm90b3R5cGUuZ2V0WSA9IHBvaW50RnBHZXRZO1xuRUNQb2ludEZwLnByb3RvdHlwZS5lcXVhbHMgPSBwb2ludEZwRXF1YWxzO1xuRUNQb2ludEZwLnByb3RvdHlwZS5pc0luZmluaXR5ID0gcG9pbnRGcElzSW5maW5pdHk7XG5FQ1BvaW50RnAucHJvdG90eXBlLm5lZ2F0ZSA9IHBvaW50RnBOZWdhdGU7XG5FQ1BvaW50RnAucHJvdG90eXBlLmFkZCA9IHBvaW50RnBBZGQ7XG5FQ1BvaW50RnAucHJvdG90eXBlLnR3aWNlID0gcG9pbnRGcFR3aWNlO1xuRUNQb2ludEZwLnByb3RvdHlwZS5tdWx0aXBseSA9IHBvaW50RnBNdWx0aXBseTtcbkVDUG9pbnRGcC5wcm90b3R5cGUubXVsdGlwbHlUd28gPSBwb2ludEZwTXVsdGlwbHlUd287XG5cbi8vIC0tLS0tLS0tLS0tLS0tLS1cbi8vIEVDQ3VydmVGcFxuXG4vLyBjb25zdHJ1Y3RvclxuZnVuY3Rpb24gRUNDdXJ2ZUZwKHEsYSxiKSB7XG4gICAgdGhpcy5xID0gcTtcbiAgICB0aGlzLmEgPSB0aGlzLmZyb21CaWdJbnRlZ2VyKGEpO1xuICAgIHRoaXMuYiA9IHRoaXMuZnJvbUJpZ0ludGVnZXIoYik7XG4gICAgdGhpcy5pbmZpbml0eSA9IG5ldyBFQ1BvaW50RnAodGhpcywgbnVsbCwgbnVsbCk7XG4gICAgdGhpcy5yZWR1Y2VyID0gbmV3IEJhcnJldHQodGhpcy5xKTtcbn1cblxuZnVuY3Rpb24gY3VydmVGcEdldFEoKSB7XG4gICAgcmV0dXJuIHRoaXMucTtcbn1cblxuZnVuY3Rpb24gY3VydmVGcEdldEEoKSB7XG4gICAgcmV0dXJuIHRoaXMuYTtcbn1cblxuZnVuY3Rpb24gY3VydmVGcEdldEIoKSB7XG4gICAgcmV0dXJuIHRoaXMuYjtcbn1cblxuZnVuY3Rpb24gY3VydmVGcEVxdWFscyhvdGhlcikge1xuICAgIGlmKG90aGVyID09IHRoaXMpIHJldHVybiB0cnVlO1xuICAgIHJldHVybih0aGlzLnEuZXF1YWxzKG90aGVyLnEpICYmIHRoaXMuYS5lcXVhbHMob3RoZXIuYSkgJiYgdGhpcy5iLmVxdWFscyhvdGhlci5iKSk7XG59XG5cbmZ1bmN0aW9uIGN1cnZlRnBHZXRJbmZpbml0eSgpIHtcbiAgICByZXR1cm4gdGhpcy5pbmZpbml0eTtcbn1cblxuZnVuY3Rpb24gY3VydmVGcEZyb21CaWdJbnRlZ2VyKHgpIHtcbiAgICByZXR1cm4gbmV3IEVDRmllbGRFbGVtZW50RnAodGhpcy5xLCB4KTtcbn1cblxuZnVuY3Rpb24gY3VydmVSZWR1Y2UoeCkge1xuICAgIHRoaXMucmVkdWNlci5yZWR1Y2UoeCk7XG59XG5cbi8vIGZvciBub3csIHdvcmsgd2l0aCBoZXggc3RyaW5ncyBiZWNhdXNlIHRoZXkncmUgZWFzaWVyIGluIEpTXG5mdW5jdGlvbiBjdXJ2ZUZwRGVjb2RlUG9pbnRIZXgocykge1xuICAgIHN3aXRjaChwYXJzZUludChzLnN1YnN0cigwLDIpLCAxNikpIHsgLy8gZmlyc3QgYnl0ZVxuICAgIGNhc2UgMDpcblx0cmV0dXJuIHRoaXMuaW5maW5pdHk7XG4gICAgY2FzZSAyOlxuICAgIGNhc2UgMzpcblx0Ly8gcG9pbnQgY29tcHJlc3Npb24gbm90IHN1cHBvcnRlZCB5ZXRcblx0cmV0dXJuIG51bGw7XG4gICAgY2FzZSA0OlxuICAgIGNhc2UgNjpcbiAgICBjYXNlIDc6XG5cdHZhciBsZW4gPSAocy5sZW5ndGggLSAyKSAvIDI7XG5cdHZhciB4SGV4ID0gcy5zdWJzdHIoMiwgbGVuKTtcblx0dmFyIHlIZXggPSBzLnN1YnN0cihsZW4rMiwgbGVuKTtcblxuXHRyZXR1cm4gbmV3IEVDUG9pbnRGcCh0aGlzLFxuXHRcdFx0ICAgICB0aGlzLmZyb21CaWdJbnRlZ2VyKG5ldyBCaWdJbnRlZ2VyKHhIZXgsIDE2KSksXG5cdFx0XHQgICAgIHRoaXMuZnJvbUJpZ0ludGVnZXIobmV3IEJpZ0ludGVnZXIoeUhleCwgMTYpKSk7XG5cbiAgICBkZWZhdWx0OiAvLyB1bnN1cHBvcnRlZFxuXHRyZXR1cm4gbnVsbDtcbiAgICB9XG59XG5cbmZ1bmN0aW9uIGN1cnZlRnBFbmNvZGVQb2ludEhleChwKSB7XG5cdGlmIChwLmlzSW5maW5pdHkoKSkgcmV0dXJuIFwiMDBcIjtcblx0dmFyIHhIZXggPSBwLmdldFgoKS50b0JpZ0ludGVnZXIoKS50b1N0cmluZygxNik7XG5cdHZhciB5SGV4ID0gcC5nZXRZKCkudG9CaWdJbnRlZ2VyKCkudG9TdHJpbmcoMTYpO1xuXHR2YXIgb0xlbiA9IHRoaXMuZ2V0USgpLnRvU3RyaW5nKDE2KS5sZW5ndGg7XG5cdGlmICgob0xlbiAlIDIpICE9IDApIG9MZW4rKztcblx0d2hpbGUgKHhIZXgubGVuZ3RoIDwgb0xlbikge1xuXHRcdHhIZXggPSBcIjBcIiArIHhIZXg7XG5cdH1cblx0d2hpbGUgKHlIZXgubGVuZ3RoIDwgb0xlbikge1xuXHRcdHlIZXggPSBcIjBcIiArIHlIZXg7XG5cdH1cblx0cmV0dXJuIFwiMDRcIiArIHhIZXggKyB5SGV4O1xufVxuXG5FQ0N1cnZlRnAucHJvdG90eXBlLmdldFEgPSBjdXJ2ZUZwR2V0UTtcbkVDQ3VydmVGcC5wcm90b3R5cGUuZ2V0QSA9IGN1cnZlRnBHZXRBO1xuRUNDdXJ2ZUZwLnByb3RvdHlwZS5nZXRCID0gY3VydmVGcEdldEI7XG5FQ0N1cnZlRnAucHJvdG90eXBlLmVxdWFscyA9IGN1cnZlRnBFcXVhbHM7XG5FQ0N1cnZlRnAucHJvdG90eXBlLmdldEluZmluaXR5ID0gY3VydmVGcEdldEluZmluaXR5O1xuRUNDdXJ2ZUZwLnByb3RvdHlwZS5mcm9tQmlnSW50ZWdlciA9IGN1cnZlRnBGcm9tQmlnSW50ZWdlcjtcbkVDQ3VydmVGcC5wcm90b3R5cGUucmVkdWNlID0gY3VydmVSZWR1Y2U7XG4vL0VDQ3VydmVGcC5wcm90b3R5cGUuZGVjb2RlUG9pbnRIZXggPSBjdXJ2ZUZwRGVjb2RlUG9pbnRIZXg7XG5FQ0N1cnZlRnAucHJvdG90eXBlLmVuY29kZVBvaW50SGV4ID0gY3VydmVGcEVuY29kZVBvaW50SGV4O1xuXG4vLyBmcm9tOiBodHRwczovL2dpdGh1Yi5jb20va2FpZWx2aW4vanNibi1lYy1wb2ludC1jb21wcmVzc2lvblxuRUNDdXJ2ZUZwLnByb3RvdHlwZS5kZWNvZGVQb2ludEhleCA9IGZ1bmN0aW9uKHMpXG57XG5cdHZhciB5SXNFdmVuO1xuICAgIHN3aXRjaChwYXJzZUludChzLnN1YnN0cigwLDIpLCAxNikpIHsgLy8gZmlyc3QgYnl0ZVxuICAgIGNhc2UgMDpcblx0cmV0dXJuIHRoaXMuaW5maW5pdHk7XG4gICAgY2FzZSAyOlxuXHR5SXNFdmVuID0gZmFsc2U7XG4gICAgY2FzZSAzOlxuXHRpZih5SXNFdmVuID09IHVuZGVmaW5lZCkgeUlzRXZlbiA9IHRydWU7XG5cdHZhciBsZW4gPSBzLmxlbmd0aCAtIDI7XG5cdHZhciB4SGV4ID0gcy5zdWJzdHIoMiwgbGVuKTtcblx0dmFyIHggPSB0aGlzLmZyb21CaWdJbnRlZ2VyKG5ldyBCaWdJbnRlZ2VyKHhIZXgsMTYpKTtcblx0dmFyIGFscGhhID0geC5tdWx0aXBseSh4LnNxdWFyZSgpLmFkZCh0aGlzLmdldEEoKSkpLmFkZCh0aGlzLmdldEIoKSk7XG5cdHZhciBiZXRhID0gYWxwaGEuc3FydCgpO1xuXG4gICAgaWYgKGJldGEgPT0gbnVsbCkgdGhyb3cgXCJJbnZhbGlkIHBvaW50IGNvbXByZXNzaW9uXCI7XG5cbiAgICB2YXIgYmV0YVZhbHVlID0gYmV0YS50b0JpZ0ludGVnZXIoKTtcbiAgICBpZiAoYmV0YVZhbHVlLnRlc3RCaXQoMCkgIT0geUlzRXZlbilcbiAgICB7XG4gICAgICAgIC8vIFVzZSB0aGUgb3RoZXIgcm9vdFxuICAgICAgICBiZXRhID0gdGhpcy5mcm9tQmlnSW50ZWdlcih0aGlzLmdldFEoKS5zdWJ0cmFjdChiZXRhVmFsdWUpKTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBFQ1BvaW50RnAodGhpcyx4LGJldGEpO1xuICAgIGNhc2UgNDpcbiAgICBjYXNlIDY6XG4gICAgY2FzZSA3OlxuXHR2YXIgbGVuID0gKHMubGVuZ3RoIC0gMikgLyAyO1xuXHR2YXIgeEhleCA9IHMuc3Vic3RyKDIsIGxlbik7XG5cdHZhciB5SGV4ID0gcy5zdWJzdHIobGVuKzIsIGxlbik7XG5cblx0cmV0dXJuIG5ldyBFQ1BvaW50RnAodGhpcyxcblx0XHRcdCAgICAgdGhpcy5mcm9tQmlnSW50ZWdlcihuZXcgQmlnSW50ZWdlcih4SGV4LCAxNikpLFxuXHRcdFx0ICAgICB0aGlzLmZyb21CaWdJbnRlZ2VyKG5ldyBCaWdJbnRlZ2VyKHlIZXgsIDE2KSkpO1xuXG4gICAgZGVmYXVsdDogLy8gdW5zdXBwb3J0ZWRcblx0cmV0dXJuIG51bGw7XG4gICAgfVxufVxuRUNDdXJ2ZUZwLnByb3RvdHlwZS5lbmNvZGVDb21wcmVzc2VkUG9pbnRIZXggPSBmdW5jdGlvbihwKVxue1xuXHRpZiAocC5pc0luZmluaXR5KCkpIHJldHVybiBcIjAwXCI7XG5cdHZhciB4SGV4ID0gcC5nZXRYKCkudG9CaWdJbnRlZ2VyKCkudG9TdHJpbmcoMTYpO1xuXHR2YXIgb0xlbiA9IHRoaXMuZ2V0USgpLnRvU3RyaW5nKDE2KS5sZW5ndGg7XG5cdGlmICgob0xlbiAlIDIpICE9IDApIG9MZW4rKztcblx0d2hpbGUgKHhIZXgubGVuZ3RoIDwgb0xlbilcblx0XHR4SGV4ID0gXCIwXCIgKyB4SGV4O1xuXHR2YXIgeVByZWZpeDtcblx0aWYocC5nZXRZKCkudG9CaWdJbnRlZ2VyKCkuaXNFdmVuKCkpIHlQcmVmaXggPSBcIjAyXCI7XG5cdGVsc2UgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB5UHJlZml4ID0gXCIwM1wiO1xuXG5cdHJldHVybiB5UHJlZml4ICsgeEhleDtcbn1cblxuXG5FQ0ZpZWxkRWxlbWVudEZwLnByb3RvdHlwZS5nZXRSID0gZnVuY3Rpb24oKVxue1xuXHRpZih0aGlzLnIgIT0gdW5kZWZpbmVkKSByZXR1cm4gdGhpcy5yO1xuXG4gICAgdGhpcy5yID0gbnVsbDtcbiAgICB2YXIgYml0TGVuZ3RoID0gdGhpcy5xLmJpdExlbmd0aCgpO1xuICAgIGlmIChiaXRMZW5ndGggPiAxMjgpXG4gICAge1xuICAgICAgICB2YXIgZmlyc3RXb3JkID0gdGhpcy5xLnNoaWZ0UmlnaHQoYml0TGVuZ3RoIC0gNjQpO1xuICAgICAgICBpZiAoZmlyc3RXb3JkLmludFZhbHVlKCkgPT0gLTEpXG4gICAgICAgIHtcbiAgICAgICAgICAgIHRoaXMuciA9IEJpZ0ludGVnZXIuT05FLnNoaWZ0TGVmdChiaXRMZW5ndGgpLnN1YnRyYWN0KHRoaXMucSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRoaXMucjtcbn1cbkVDRmllbGRFbGVtZW50RnAucHJvdG90eXBlLm1vZE11bHQgPSBmdW5jdGlvbih4MSx4MilcbntcbiAgICByZXR1cm4gdGhpcy5tb2RSZWR1Y2UoeDEubXVsdGlwbHkoeDIpKTtcbn1cbkVDRmllbGRFbGVtZW50RnAucHJvdG90eXBlLm1vZFJlZHVjZSA9IGZ1bmN0aW9uKHgpXG57XG4gICAgaWYgKHRoaXMuZ2V0UigpICE9IG51bGwpXG4gICAge1xuICAgICAgICB2YXIgcUxlbiA9IHEuYml0TGVuZ3RoKCk7XG4gICAgICAgIHdoaWxlICh4LmJpdExlbmd0aCgpID4gKHFMZW4gKyAxKSlcbiAgICAgICAge1xuICAgICAgICAgICAgdmFyIHUgPSB4LnNoaWZ0UmlnaHQocUxlbik7XG4gICAgICAgICAgICB2YXIgdiA9IHguc3VidHJhY3QodS5zaGlmdExlZnQocUxlbikpO1xuICAgICAgICAgICAgaWYgKCF0aGlzLmdldFIoKS5lcXVhbHMoQmlnSW50ZWdlci5PTkUpKVxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIHUgPSB1Lm11bHRpcGx5KHRoaXMuZ2V0UigpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHggPSB1LmFkZCh2KTsgXG4gICAgICAgIH1cbiAgICAgICAgd2hpbGUgKHguY29tcGFyZVRvKHEpID49IDApXG4gICAgICAgIHtcbiAgICAgICAgICAgIHggPSB4LnN1YnRyYWN0KHEpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2VcbiAgICB7XG4gICAgICAgIHggPSB4Lm1vZChxKTtcbiAgICB9XG4gICAgcmV0dXJuIHg7XG59XG5FQ0ZpZWxkRWxlbWVudEZwLnByb3RvdHlwZS5zcXJ0ID0gZnVuY3Rpb24oKVxue1xuICAgIGlmICghdGhpcy5xLnRlc3RCaXQoMCkpIHRocm93IFwidW5zdXBwb3J0ZWRcIjtcblxuICAgIC8vIHAgbW9kIDQgPT0gM1xuICAgIGlmICh0aGlzLnEudGVzdEJpdCgxKSlcbiAgICB7XG4gICAgXHR2YXIgeiA9IG5ldyBFQ0ZpZWxkRWxlbWVudEZwKHRoaXMucSx0aGlzLngubW9kUG93KHRoaXMucS5zaGlmdFJpZ2h0KDIpLmFkZChCaWdJbnRlZ2VyLk9ORSksdGhpcy5xKSk7XG4gICAgXHRyZXR1cm4gei5zcXVhcmUoKS5lcXVhbHModGhpcykgPyB6IDogbnVsbDtcbiAgICB9XG5cbiAgICAvLyBwIG1vZCA0ID09IDFcbiAgICB2YXIgcU1pbnVzT25lID0gdGhpcy5xLnN1YnRyYWN0KEJpZ0ludGVnZXIuT05FKTtcblxuICAgIHZhciBsZWdlbmRyZUV4cG9uZW50ID0gcU1pbnVzT25lLnNoaWZ0UmlnaHQoMSk7XG4gICAgaWYgKCEodGhpcy54Lm1vZFBvdyhsZWdlbmRyZUV4cG9uZW50LCB0aGlzLnEpLmVxdWFscyhCaWdJbnRlZ2VyLk9ORSkpKVxuICAgIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgdmFyIHUgPSBxTWludXNPbmUuc2hpZnRSaWdodCgyKTtcbiAgICB2YXIgayA9IHUuc2hpZnRMZWZ0KDEpLmFkZChCaWdJbnRlZ2VyLk9ORSk7XG5cbiAgICB2YXIgUSA9IHRoaXMueDtcbiAgICB2YXIgZm91clEgPSBtb2REb3VibGUobW9kRG91YmxlKFEpKTtcblxuICAgIHZhciBVLCBWO1xuICAgIGRvXG4gICAge1xuICAgICAgICB2YXIgUDtcbiAgICAgICAgZG9cbiAgICAgICAge1xuICAgICAgICAgICAgUCA9IG5ldyBCaWdJbnRlZ2VyKHRoaXMucS5iaXRMZW5ndGgoKSwgbmV3IFNlY3VyZVJhbmRvbSgpKTtcbiAgICAgICAgfVxuICAgICAgICB3aGlsZSAoUC5jb21wYXJlVG8odGhpcy5xKSA+PSAwXG4gICAgICAgICAgICB8fCAhKFAubXVsdGlwbHkoUCkuc3VidHJhY3QoZm91clEpLm1vZFBvdyhsZWdlbmRyZUV4cG9uZW50LCB0aGlzLnEpLmVxdWFscyhxTWludXNPbmUpKSk7XG5cbiAgICAgICAgdmFyIHJlc3VsdCA9IHRoaXMubHVjYXNTZXF1ZW5jZShQLCBRLCBrKTtcbiAgICAgICAgVSA9IHJlc3VsdFswXTtcbiAgICAgICAgViA9IHJlc3VsdFsxXTtcblxuICAgICAgICBpZiAodGhpcy5tb2RNdWx0KFYsIFYpLmVxdWFscyhmb3VyUSkpXG4gICAgICAgIHtcbiAgICAgICAgICAgIC8vIEludGVnZXIgZGl2aXNpb24gYnkgMiwgbW9kIHFcbiAgICAgICAgICAgIGlmIChWLnRlc3RCaXQoMCkpXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgViA9IFYuYWRkKHEpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBWID0gVi5zaGlmdFJpZ2h0KDEpO1xuXG4gICAgICAgICAgICByZXR1cm4gbmV3IEVDRmllbGRFbGVtZW50RnAocSxWKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICB3aGlsZSAoVS5lcXVhbHMoQmlnSW50ZWdlci5PTkUpIHx8IFUuZXF1YWxzKHFNaW51c09uZSkpO1xuXG4gICAgcmV0dXJuIG51bGw7XG59XG5FQ0ZpZWxkRWxlbWVudEZwLnByb3RvdHlwZS5sdWNhc1NlcXVlbmNlID0gZnVuY3Rpb24oUCxRLGspXG57XG4gICAgdmFyIG4gPSBrLmJpdExlbmd0aCgpO1xuICAgIHZhciBzID0gay5nZXRMb3dlc3RTZXRCaXQoKTtcblxuICAgIHZhciBVaCA9IEJpZ0ludGVnZXIuT05FO1xuICAgIHZhciBWbCA9IEJpZ0ludGVnZXIuVFdPO1xuICAgIHZhciBWaCA9IFA7XG4gICAgdmFyIFFsID0gQmlnSW50ZWdlci5PTkU7XG4gICAgdmFyIFFoID0gQmlnSW50ZWdlci5PTkU7XG5cbiAgICBmb3IgKHZhciBqID0gbiAtIDE7IGogPj0gcyArIDE7IC0tailcbiAgICB7XG4gICAgICAgIFFsID0gdGhpcy5tb2RNdWx0KFFsLCBRaCk7XG5cbiAgICAgICAgaWYgKGsudGVzdEJpdChqKSlcbiAgICAgICAge1xuICAgICAgICAgICAgUWggPSB0aGlzLm1vZE11bHQoUWwsIFEpO1xuICAgICAgICAgICAgVWggPSB0aGlzLm1vZE11bHQoVWgsIFZoKTtcbiAgICAgICAgICAgIFZsID0gdGhpcy5tb2RSZWR1Y2UoVmgubXVsdGlwbHkoVmwpLnN1YnRyYWN0KFAubXVsdGlwbHkoUWwpKSk7XG4gICAgICAgICAgICBWaCA9IHRoaXMubW9kUmVkdWNlKFZoLm11bHRpcGx5KFZoKS5zdWJ0cmFjdChRaC5zaGlmdExlZnQoMSkpKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlXG4gICAgICAgIHtcbiAgICAgICAgICAgIFFoID0gUWw7XG4gICAgICAgICAgICBVaCA9IHRoaXMubW9kUmVkdWNlKFVoLm11bHRpcGx5KFZsKS5zdWJ0cmFjdChRbCkpO1xuICAgICAgICAgICAgVmggPSB0aGlzLm1vZFJlZHVjZShWaC5tdWx0aXBseShWbCkuc3VidHJhY3QoUC5tdWx0aXBseShRbCkpKTtcbiAgICAgICAgICAgIFZsID0gdGhpcy5tb2RSZWR1Y2UoVmwubXVsdGlwbHkoVmwpLnN1YnRyYWN0KFFsLnNoaWZ0TGVmdCgxKSkpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgUWwgPSB0aGlzLm1vZE11bHQoUWwsIFFoKTtcbiAgICBRaCA9IHRoaXMubW9kTXVsdChRbCwgUSk7XG4gICAgVWggPSB0aGlzLm1vZFJlZHVjZShVaC5tdWx0aXBseShWbCkuc3VidHJhY3QoUWwpKTtcbiAgICBWbCA9IHRoaXMubW9kUmVkdWNlKFZoLm11bHRpcGx5KFZsKS5zdWJ0cmFjdChQLm11bHRpcGx5KFFsKSkpO1xuICAgIFFsID0gdGhpcy5tb2RNdWx0KFFsLCBRaCk7XG5cbiAgICBmb3IgKHZhciBqID0gMTsgaiA8PSBzOyArK2opXG4gICAge1xuICAgICAgICBVaCA9IHRoaXMubW9kTXVsdChVaCwgVmwpO1xuICAgICAgICBWbCA9IHRoaXMubW9kUmVkdWNlKFZsLm11bHRpcGx5KFZsKS5zdWJ0cmFjdChRbC5zaGlmdExlZnQoMSkpKTtcbiAgICAgICAgUWwgPSB0aGlzLm1vZE11bHQoUWwsIFFsKTtcbiAgICB9XG5cbiAgICByZXR1cm4gWyBVaCwgVmwgXTtcbn1cblxudmFyIGV4cG9ydHMgPSB7XG4gIEVDQ3VydmVGcDogRUNDdXJ2ZUZwLFxuICBFQ1BvaW50RnA6IEVDUG9pbnRGcCxcbiAgRUNGaWVsZEVsZW1lbnRGcDogRUNGaWVsZEVsZW1lbnRGcFxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGV4cG9ydHNcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ecc-jsbn/lib/ec.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ecc-jsbn/lib/sec.js":
/*!******************************************!*\
  !*** ./node_modules/ecc-jsbn/lib/sec.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Named EC curves\n\n// Requires ec.js, jsbn.js, and jsbn2.js\nvar BigInteger = (__webpack_require__(/*! jsbn */ \"(rsc)/./node_modules/jsbn/index.js\").BigInteger)\nvar ECCurveFp = (__webpack_require__(/*! ./ec.js */ \"(rsc)/./node_modules/ecc-jsbn/lib/ec.js\").ECCurveFp)\n\n\n// ----------------\n// X9ECParameters\n\n// constructor\nfunction X9ECParameters(curve,g,n,h) {\n    this.curve = curve;\n    this.g = g;\n    this.n = n;\n    this.h = h;\n}\n\nfunction x9getCurve() {\n    return this.curve;\n}\n\nfunction x9getG() {\n    return this.g;\n}\n\nfunction x9getN() {\n    return this.n;\n}\n\nfunction x9getH() {\n    return this.h;\n}\n\nX9ECParameters.prototype.getCurve = x9getCurve;\nX9ECParameters.prototype.getG = x9getG;\nX9ECParameters.prototype.getN = x9getN;\nX9ECParameters.prototype.getH = x9getH;\n\n// ----------------\n// SECNamedCurves\n\nfunction fromHex(s) { return new BigInteger(s, 16); }\n\nfunction secp128r1() {\n    // p = 2^128 - 2^97 - 1\n    var p = fromHex(\"FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFF\");\n    var a = fromHex(\"FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFC\");\n    var b = fromHex(\"E87579C11079F43DD824993C2CEE5ED3\");\n    //byte[] S = Hex.decode(\"000E0D4D696E6768756151750CC03A4473D03679\");\n    var n = fromHex(\"FFFFFFFE0000000075A30D1B9038A115\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"161FF7528B899B2D0C28607CA52C5B86\"\n\t\t+ \"CF5AC8395BAFEB13C02DA292DDED7A83\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp160k1() {\n    // p = 2^160 - 2^32 - 2^14 - 2^12 - 2^9 - 2^8 - 2^7 - 2^3 - 2^2 - 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73\");\n    var a = BigInteger.ZERO;\n    var b = fromHex(\"7\");\n    //byte[] S = null;\n    var n = fromHex(\"0100000000000000000001B8FA16DFAB9ACA16B6B3\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"3B4C382CE37AA192A4019E763036F4F5DD4D7EBB\"\n                + \"938CF935318FDCED6BC28286531733C3F03C4FEE\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp160r1() {\n    // p = 2^160 - 2^31 - 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFF\");\n    var a = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFC\");\n    var b = fromHex(\"1C97BEFC54BD7A8B65ACF89F81D4D4ADC565FA45\");\n    //byte[] S = Hex.decode(\"1053CDE42C14D696E67687561517533BF3F83345\");\n    var n = fromHex(\"0100000000000000000001F4C8F927AED3CA752257\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n\t\t+ \"4A96B5688EF573284664698968C38BB913CBFC82\"\n\t\t+ \"23A628553168947D59DCC912042351377AC5FB32\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp192k1() {\n    // p = 2^192 - 2^32 - 2^12 - 2^8 - 2^7 - 2^6 - 2^3 - 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFEE37\");\n    var a = BigInteger.ZERO;\n    var b = fromHex(\"3\");\n    //byte[] S = null;\n    var n = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFE26F2FC170F69466A74DEFD8D\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"DB4FF10EC057E9AE26B07D0280B7F4341DA5D1B1EAE06C7D\"\n                + \"9B2F2F6D9C5628A7844163D015BE86344082AA88D95E2F9D\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp192r1() {\n    // p = 2^192 - 2^64 - 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF\");\n    var a = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFC\");\n    var b = fromHex(\"64210519E59C80E70FA7E9AB72243049FEB8DEECC146B9B1\");\n    //byte[] S = Hex.decode(\"3045AE6FC8422F64ED579528D38120EAE12196D5\");\n    var n = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"188DA80EB03090F67CBF20EB43A18800F4FF0AFD82FF1012\"\n                + \"07192B95FFC8DA78631011ED6B24CDD573F977A11E794811\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp224r1() {\n    // p = 2^224 - 2^96 + 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF000000000000000000000001\");\n    var a = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFE\");\n    var b = fromHex(\"B4050A850C04B3ABF54132565044B0B7D7BFD8BA270B39432355FFB4\");\n    //byte[] S = Hex.decode(\"BD71344799D5C7FCDC45B59FA3B9AB8F6A948BC5\");\n    var n = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFF16A2E0B8F03E13DD29455C5C2A3D\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"B70E0CBD6BB4BF7F321390B94A03C1D356C21122343280D6115C1D21\"\n                + \"BD376388B5F723FB4C22DFE6CD4375A05A07476444D5819985007E34\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp256r1() {\n    // p = 2^224 (2^32 - 1) + 2^192 + 2^96 - 1\n    var p = fromHex(\"FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF\");\n    var a = fromHex(\"FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFC\");\n    var b = fromHex(\"5AC635D8AA3A93E7B3EBBD55769886BC651D06B0CC53B0F63BCE3C3E27D2604B\");\n    //byte[] S = Hex.decode(\"C49D360886E704936A6678E1139D26B7819F7E90\");\n    var n = fromHex(\"FFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"6B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C296\"\n\t\t+ \"4FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\n// TODO: make this into a proper hashtable\nfunction getSECCurveByName(name) {\n    if(name == \"secp128r1\") return secp128r1();\n    if(name == \"secp160k1\") return secp160k1();\n    if(name == \"secp160r1\") return secp160r1();\n    if(name == \"secp192k1\") return secp192k1();\n    if(name == \"secp192r1\") return secp192r1();\n    if(name == \"secp224r1\") return secp224r1();\n    if(name == \"secp256r1\") return secp256r1();\n    return null;\n}\n\nmodule.exports = {\n  \"secp128r1\":secp128r1,\n  \"secp160k1\":secp160k1,\n  \"secp160r1\":secp160r1,\n  \"secp192k1\":secp192k1,\n  \"secp192r1\":secp192r1,\n  \"secp224r1\":secp224r1,\n  \"secp256r1\":secp256r1\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ecc-jsbn/lib/sec.js\n");

/***/ })

};
;