{"name": "jiti", "version": "1.21.0", "description": "Runtime typescript and ESM support for Node.js", "repository": "unjs/jiti", "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": "bin/jiti.js", "files": ["lib", "dist", "register.js"], "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-export-namespace-from": "^7.22.11", "@babel/plugin-transform-modules-commonjs": "^7.23.0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.11", "@babel/plugin-transform-optional-chaining": "^7.23.0", "@babel/plugin-transform-typescript": "^7.22.15", "@babel/preset-typescript": "^7.23.2", "@babel/template": "^7.22.15", "@babel/types": "^7.23.0", "@types/babel__core": "^7.20.3", "@types/babel__template": "^7.4.3", "@types/node": "^20.8.9", "@types/object-hash": "^3.0.5", "@types/resolve": "^1.20.4", "@types/semver": "^7.5.4", "@vitest/coverage-v8": "^0.34.6", "acorn": "^8.11.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.9", "create-require": "^1.1.1", "destr": "^2.0.2", "escape-string-regexp": "^5.0.0", "eslint": "^8.52.0", "eslint-config-unjs": "^0.2.1", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^8.0.1", "fast-glob": "^3.3.1", "mlly": "^1.4.2", "object-hash": "^3.0.0", "pathe": "^1.1.1", "pirates": "^4.0.6", "pkg-types": "^1.0.3", "prettier": "^3.0.3", "reflect-metadata": "^0.1.13", "semver": "^7.5.4", "std-env": "^3.4.3", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.5.0", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@8.8.0"}