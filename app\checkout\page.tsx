"use client";
import * as React from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useRouter, useSearchParams } from "next/navigation";
import Script from "next/script";
import {
  LoaderCircle,
  ArrowLeft,
  Shield,
  CreditCard,
  Lock,
  CheckCircle,
  Sparkles,
} from "lucide-react";
import { apiClient, authStorage } from "@/lib/api";
import Header from "@/components/Header";

export default function Checkout() {
  const router = useRouter();
  const params = useSearchParams();
  const planId = params.get("planId");
  const durationId = params.get("durationId");
  const price = params.get("price");
  const couponCode = params.get("couponCode") || "";
  const [loading1, setLoading1] = React.useState(true);
  const [loading, setLoading] = React.useState(false);
  const [user, setUser] = React.useState<any>(null);
  const [isCreatingOrder, setIsCreatingOrder] = React.useState(false);
  const orderIdRef = React.useRef<string>();
  const [orderData, setOrderData] = React.useState<any>(null);

  const createOrderId = React.useCallback(
    async (token: string) => {
      // Prevent duplicate calls
      if (isCreatingOrder || orderIdRef.current) {
        return;
      }

      try {
        setIsCreatingOrder(true);
        const response = await apiClient.createOrder(
          {
            planId: planId!,
            durationId: durationId!,
            couponCode: couponCode || undefined,
          },
          token
        );

        // Store the full order data and use the razorpayOrderId
        setOrderData(response.data);
        orderIdRef.current = response.data.razorpayOrderId;
        setLoading1(false);
      } catch (error) {
        console.error("There was a problem creating the order:", error);
        setLoading1(false);
        setIsCreatingOrder(false);
        alert("Failed to create order. Please try again.");
      } finally {
        setIsCreatingOrder(false);
      }
    },
    [planId, durationId, couponCode, isCreatingOrder]
  );

  React.useEffect(() => {
    const auth = authStorage.getAuth();
    if (!auth || !authStorage.isAuthenticated()) {
      router.replace("/login");
      return;
    }

    if (!planId || !durationId || !price) {
      router.replace("/");
      return;
    }

    // Only create order if we don't already have one and not already creating
    if (!user) {
      setUser(auth);
    }

    if (!orderIdRef.current && !isCreatingOrder && !orderData) {
      createOrderId(auth.accessToken);
    }
  }, [planId, durationId, price, user, isCreatingOrder, orderData]); // Add proper dependencies

  const processPayment = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    const orderId = orderIdRef.current;

    if (!orderId) {
      alert("Order ID not found. Please try again.");
      setLoading(false);
      return;
    }

    try {
      const options = {
        key: orderData?.key || process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: orderData?.amount || parseFloat(price!) * 100,
        currency: orderData?.currency || "INR",
        name: "Workout Plan Payment",
        description: "Payment for workout plan subscription",
        order_id: orderId,
        handler: async function (response: any) {
          try {
            const verificationResult = await apiClient.verifyPayment(
              response.razorpay_order_id, // Use the Razorpay order ID from the payment response
              response.razorpay_payment_id,
              response.razorpay_signature,
              user.accessToken
            );

            console.log("Verification result:", verificationResult);

            // Check if verification was successful
            // Your backend returns { status: "Success", data: { order: {...}, subscription: {...} } }
            if (
              verificationResult &&
              (verificationResult.status === "Success" ||
                verificationResult.data?.order ||
                verificationResult.order)
            ) {
              alert("Payment successful!");
              router.push("/success");
            } else {
              alert("Payment verification failed");
            }
          } catch (error) {
            console.error("Payment verification error:", error);

            // Check if it's a network error vs validation error
            if (error instanceof Error) {
              if (error.message.includes("Payment already verified")) {
                alert("Payment successful!");
                router.push("/success");
              } else {
                alert(`Payment verification failed: ${error.message}`);
              }
            } else {
              alert("Payment verification failed due to network error");
            }
          }
        },
        theme: {
          color: "#3399cc",
        },
      };

      const paymentObject = new window.Razorpay(options);
      paymentObject.on("payment.failed", function (response: any) {
        alert(response.error.description);
      });
      setLoading(false);
      paymentObject.open();
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  if (loading1)
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
        <Header user={user} />
        <div className="flex justify-center items-center h-[80vh]">
          <div className="text-center">
            <LoaderCircle className="animate-spin h-16 w-16 text-purple-600 mx-auto" />

            <p className="text-gray-600 font-medium">
              Preparing your checkout...
            </p>
          </div>
        </div>
      </div>
    );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50">
      <Script
        id="razorpay-checkout-js"
        src="https://checkout.razorpay.com/v1/checkout.js"
      />

      <Header user={user} />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
            Secure Checkout
          </h1>
          <p className="text-base md:text-lg text-gray-600">
            Complete your purchase with our secure payment gateway.
          </p>
        </div>

        {/* Checkout Card */}
        <div className="max-w-2xl mx-auto">
          <div className="gradient-card rounded-3xl p-8 shadow-2xl animate-scale-in">
            {/* Security Badge */}
            <div className="flex items-center justify-center mb-8">
              <div className="flex items-center space-x-2 px-4 py-2 bg-green-50 rounded-full border border-green-200">
                <Shield className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-700">
                  256-bit SSL Encrypted
                </span>
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-white/50 rounded-2xl p-6 mb-8 border border-white/30">
              <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <Sparkles className="h-5 w-5 text-purple-600 mr-2" />
                Order Summary
              </h3>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Plan Amount</span>
                  <span className="font-semibold">₹{price}</span>
                </div>

                {couponCode && (
                  <div className="flex justify-between items-center text-green-600">
                    <span className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Coupon Applied ({couponCode})
                    </span>
                    <span className="font-semibold">Discount Applied</span>
                  </div>
                )}

                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>Total Amount</span>
                    <span className="text-purple-600">₹{price}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Button */}
            <form onSubmit={processPayment} className="space-y-6">
              <button
                type="submit"
                disabled={loading}
                className="btn-gradient w-full h-16 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed group"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <LoaderCircle className="animate-spin h-6 w-6 mr-3" />
                    Processing...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <Lock className="h-6 w-6 mr-3 group-hover:scale-110 transition-transform duration-300" />
                    Pay Securely ₹{price}
                  </div>
                )}
              </button>

              {/* Security Info */}
              <div className="text-center space-y-2">
                <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <CreditCard className="h-4 w-4 mr-1" />
                    <span>All Cards Accepted</span>
                  </div>
                  <div className="flex items-center">
                    <Shield className="h-4 w-4 mr-1" />
                    <span>100% Secure</span>
                  </div>
                </div>
                <p className="text-xs text-gray-400">
                  Your payment information is encrypted and secure. We never
                  store your card details.
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
