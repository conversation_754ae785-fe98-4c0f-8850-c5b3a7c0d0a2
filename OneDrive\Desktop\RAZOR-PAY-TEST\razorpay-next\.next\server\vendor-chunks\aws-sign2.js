/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/aws-sign2";
exports.ids = ["vendor-chunks/aws-sign2"];
exports.modules = {

/***/ "(rsc)/./node_modules/aws-sign2/index.js":
/*!*****************************************!*\
  !*** ./node_modules/aws-sign2/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/*!\n *  Copyright 2010 LearnBoost <<EMAIL>>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Module dependencies.\n */\n\nvar crypto = __webpack_require__(/*! crypto */ \"crypto\")\n  , parse = (__webpack_require__(/*! url */ \"url\").parse)\n  ;\n\n/**\n * Valid keys.\n */\n\nvar keys = \n  [ 'acl'\n  , 'location'\n  , 'logging'\n  , 'notification'\n  , 'partNumber'\n  , 'policy'\n  , 'requestPayment'\n  , 'torrent'\n  , 'uploadId'\n  , 'uploads'\n  , 'versionId'\n  , 'versioning'\n  , 'versions'\n  , 'website'\n  ]\n\n/**\n * Return an \"Authorization\" header value with the given `options`\n * in the form of \"AWS <key>:<signature>\"\n *\n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction authorization (options) {\n  return 'AWS ' + options.key + ':' + sign(options)\n}\n\nmodule.exports = authorization\nmodule.exports.authorization = authorization\n\n/**\n * Simple HMAC-SHA1 Wrapper\n *\n * @param {Object} options\n * @return {String}\n * @api private\n */ \n\nfunction hmacSha1 (options) {\n  return crypto.createHmac('sha1', options.secret).update(options.message).digest('base64')\n}\n\nmodule.exports.hmacSha1 = hmacSha1\n\n/**\n * Create a base64 sha1 HMAC for `options`. \n * \n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction sign (options) {\n  options.message = stringToSign(options)\n  return hmacSha1(options)\n}\nmodule.exports.sign = sign\n\n/**\n * Create a base64 sha1 HMAC for `options`. \n *\n * Specifically to be used with S3 presigned URLs\n * \n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction signQuery (options) {\n  options.message = queryStringToSign(options)\n  return hmacSha1(options)\n}\nmodule.exports.signQuery= signQuery\n\n/**\n * Return a string for sign() with the given `options`.\n *\n * Spec:\n * \n *    <verb>\\n\n *    <md5>\\n\n *    <content-type>\\n\n *    <date>\\n\n *    [headers\\n]\n *    <resource>\n *\n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction stringToSign (options) {\n  var headers = options.amazonHeaders || ''\n  if (headers) headers += '\\n'\n  var r = \n    [ options.verb\n    , options.md5\n    , options.contentType\n    , options.date ? options.date.toUTCString() : ''\n    , headers + options.resource\n    ]\n  return r.join('\\n')\n}\nmodule.exports.stringToSign = stringToSign\n\n/**\n * Return a string for sign() with the given `options`, but is meant exclusively\n * for S3 presigned URLs\n *\n * Spec:\n * \n *    <date>\\n\n *    <resource>\n *\n * @param {Object} options\n * @return {String}\n * @api private\n */\n\nfunction queryStringToSign (options){\n  return 'GET\\n\\n\\n' + options.date + '\\n' + options.resource\n}\nmodule.exports.queryStringToSign = queryStringToSign\n\n/**\n * Perform the following:\n *\n *  - ignore non-amazon headers\n *  - lowercase fields\n *  - sort lexicographically\n *  - trim whitespace between \":\"\n *  - join with newline\n *\n * @param {Object} headers\n * @return {String}\n * @api private\n */\n\nfunction canonicalizeHeaders (headers) {\n  var buf = []\n    , fields = Object.keys(headers)\n    ;\n  for (var i = 0, len = fields.length; i < len; ++i) {\n    var field = fields[i]\n      , val = headers[field]\n      , field = field.toLowerCase()\n      ;\n    if (0 !== field.indexOf('x-amz')) continue\n    buf.push(field + ':' + val)\n  }\n  return buf.sort().join('\\n')\n}\nmodule.exports.canonicalizeHeaders = canonicalizeHeaders\n\n/**\n * Perform the following:\n *\n *  - ignore non sub-resources\n *  - sort lexicographically\n *\n * @param {String} resource\n * @return {String}\n * @api private\n */\n\nfunction canonicalizeResource (resource) {\n  var url = parse(resource, true)\n    , path = url.pathname\n    , buf = []\n    ;\n\n  Object.keys(url.query).forEach(function(key){\n    if (!~keys.indexOf(key)) return\n    var val = '' == url.query[key] ? '' : '=' + encodeURIComponent(url.query[key])\n    buf.push(key + val)\n  })\n\n  return path + (buf.length ? '?' + buf.sort().join('&') : '')\n}\nmodule.exports.canonicalizeResource = canonicalizeResource\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/aws-sign2/index.js\n");

/***/ })

};
;