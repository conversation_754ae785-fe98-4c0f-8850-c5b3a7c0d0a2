const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResponse {
  status: string;
  message: string;
  data: {
    email: string;
    firstName: string;
    lastName: string;
    phoneNumber: string | null;
    dateOfBirth: string | null;
    avatar: string | null;
    createdAt: string;
    updatedAt: string;
    accessToken: string;
    accessTokenExp: number;
    refreshToken: string;
    refreshTokenExp: number;
  };
}

export interface Plan {
  id: string;
  name: string;
  slug: string;
  description: string;
  durations: Duration[];
  isActive: boolean;
  isPopular: boolean;
  customSort: number;
  createdAt: string;
  updatedAt: string;
}

export interface PlansResponse {
  status: string;
  message: string;
  data: Plan[];
}

export interface Duration {
  id: string;
  label: string;
  valueInDays: number;
  slug: string;
  features: Feature[];
  price: number;
  currency: string;
  paymentType: string;
  isActive: boolean;
  customSort: number;
  createdAt: string;
  updatedAt: string;
}

export interface Feature {
  id: string;
  name: string;
  description: string;
  slug: string;
  isActive: boolean;
  customSort: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateOrderRequest {
  planId: string;
  durationId: string;
  couponCode?: string;
}

export interface PaymentVerificationResponse {
  status?: string;
  message?: string;
  data?: {
    order: {
      id: string;
      status: string;
      amount: number;
      currency: string;
      razorpayOrderId: string;
      razorpayPaymentId: string;
      razorpaySignature: string;
    };
    subscription: {
      id: string;
      status: string;
      startDate: string;
      endDate: string;
      isActive: boolean;
    } | null;
  };
  // For backward compatibility
  order?: {
    id: string;
    status: string;
    amount: number;
    currency: string;
    razorpayOrderId: string;
    razorpayPaymentId: string;
    razorpaySignature: string;
  };
  subscription?: {
    id: string;
    status: string;
    startDate: string;
    endDate: string;
    isActive: boolean;
  } | null;
}

export interface CreateOrderResponse {
  status: string;
  message: string;
  data: {
    orderId: string;
    razorpayOrderId: string;
    amount: number;
    currency: string;
    key: string;
    planDetails: {
      planId: string;
      durationId: string;
    };
    receipt: string;
    status: string;
    createdAt: number;
  };
}

// API utility functions
export const apiClient = {
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await fetch("/api/auth/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Login failed");
    }

    return response.json();
  },

  async getPlans(token: string): Promise<Plan[]> {
    const response = await fetch("/api/plans", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to fetch plans");
    }

    const result: PlansResponse = await response.json();
    return result.data;
  },

  async createOrder(
    orderData: CreateOrderRequest,
    token: string
  ): Promise<CreateOrderResponse> {
    const response = await fetch("/api/order", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(orderData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to create order");
    }

    return response.json();
  },

  async verifyPayment(
    razorpay_order_id: string,
    razorpay_payment_id: string,
    razorpay_signature: string,
    token: string
  ): Promise<PaymentVerificationResponse> {
    const response = await fetch("/api/verify", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Payment verification failed");
    }

    return response.json();
  },
};

// Local storage utilities for auth
export const authStorage = {
  setAuth(data: LoginResponse["data"]) {
    localStorage.setItem("auth", JSON.stringify(data));
  },

  getAuth(): LoginResponse["data"] | null {
    const auth = localStorage.getItem("auth");
    return auth ? JSON.parse(auth) : null;
  },

  clearAuth() {
    localStorage.removeItem("auth");
  },

  isAuthenticated(): boolean {
    const auth = this.getAuth();
    if (!auth) return false;

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    return auth.accessTokenExp > now;
  },

  getToken(): string | null {
    const auth = this.getAuth();
    return auth?.accessToken || null;
  },
};
