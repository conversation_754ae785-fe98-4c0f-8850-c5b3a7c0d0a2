/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/aws4";
exports.ids = ["vendor-chunks/aws4"];
exports.modules = {

/***/ "(rsc)/./node_modules/aws4/aws4.js":
/*!***********************************!*\
  !*** ./node_modules/aws4/aws4.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var aws4 = exports,\n    url = __webpack_require__(/*! url */ \"url\"),\n    querystring = __webpack_require__(/*! querystring */ \"querystring\"),\n    crypto = __webpack_require__(/*! crypto */ \"crypto\"),\n    lru = __webpack_require__(/*! ./lru */ \"(rsc)/./node_modules/aws4/lru.js\"),\n    credentialsCache = lru(1000)\n\n// http://docs.amazonwebservices.com/general/latest/gr/signature-version-4.html\n\nfunction hmac(key, string, encoding) {\n  return crypto.createHmac('sha256', key).update(string, 'utf8').digest(encoding)\n}\n\nfunction hash(string, encoding) {\n  return crypto.createHash('sha256').update(string, 'utf8').digest(encoding)\n}\n\n// This function assumes the string has already been percent encoded\nfunction encodeRfc3986(urlEncodedString) {\n  return urlEncodedString.replace(/[!'()*]/g, function(c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\nfunction encodeRfc3986Full(str) {\n  return encodeRfc3986(encodeURIComponent(str))\n}\n\n// A bit of a combination of:\n// https://github.com/aws/aws-sdk-java-v2/blob/dc695de6ab49ad03934e1b02e7263abbd2354be0/core/auth/src/main/java/software/amazon/awssdk/auth/signer/internal/AbstractAws4Signer.java#L59\n// https://github.com/aws/aws-sdk-js/blob/****************************************/lib/signers/v4.js#L191-L199\n// https://github.com/mhart/aws4fetch/blob/b3aed16b6f17384cf36ea33bcba3c1e9f3bdfefd/src/main.js#L25-L34\nvar HEADERS_TO_IGNORE = {\n  'authorization': true,\n  'connection': true,\n  'x-amzn-trace-id': true,\n  'user-agent': true,\n  'expect': true,\n  'presigned-expires': true,\n  'range': true,\n}\n\n// request: { path | body, [host], [method], [headers], [service], [region] }\n// credentials: { accessKeyId, secretAccessKey, [sessionToken] }\nfunction RequestSigner(request, credentials) {\n\n  if (typeof request === 'string') request = url.parse(request)\n\n  var headers = request.headers = (request.headers || {}),\n      hostParts = (!this.service || !this.region) && this.matchHost(request.hostname || request.host || headers.Host || headers.host)\n\n  this.request = request\n  this.credentials = credentials || this.defaultCredentials()\n\n  this.service = request.service || hostParts[0] || ''\n  this.region = request.region || hostParts[1] || 'us-east-1'\n\n  // SES uses a different domain from the service name\n  if (this.service === 'email') this.service = 'ses'\n\n  if (!request.method && request.body)\n    request.method = 'POST'\n\n  if (!headers.Host && !headers.host) {\n    headers.Host = request.hostname || request.host || this.createHost()\n\n    // If a port is specified explicitly, use it as is\n    if (request.port)\n      headers.Host += ':' + request.port\n  }\n  if (!request.hostname && !request.host)\n    request.hostname = headers.Host || headers.host\n\n  this.isCodeCommitGit = this.service === 'codecommit' && request.method === 'GIT'\n\n  this.extraHeadersToIgnore = request.extraHeadersToIgnore || Object.create(null)\n  this.extraHeadersToInclude = request.extraHeadersToInclude || Object.create(null)\n}\n\nRequestSigner.prototype.matchHost = function(host) {\n  var match = (host || '').match(/([^\\.]+)\\.(?:([^\\.]*)\\.)?amazonaws\\.com(\\.cn)?$/)\n  var hostParts = (match || []).slice(1, 3)\n\n  // ES's hostParts are sometimes the other way round, if the value that is expected\n  // to be region equals ‘es’ switch them back\n  // e.g. search-cluster-name-aaaa00aaaa0aaa0aaaaaaa0aaa.us-east-1.es.amazonaws.com\n  if (hostParts[1] === 'es' || hostParts[1] === 'aoss')\n    hostParts = hostParts.reverse()\n\n  if (hostParts[1] == 's3') {\n    hostParts[0] = 's3'\n    hostParts[1] = 'us-east-1'\n  } else {\n    for (var i = 0; i < 2; i++) {\n      if (/^s3-/.test(hostParts[i])) {\n        hostParts[1] = hostParts[i].slice(3)\n        hostParts[0] = 's3'\n        break\n      }\n    }\n  }\n\n  return hostParts\n}\n\n// http://docs.aws.amazon.com/general/latest/gr/rande.html\nRequestSigner.prototype.isSingleRegion = function() {\n  // Special case for S3 and SimpleDB in us-east-1\n  if (['s3', 'sdb'].indexOf(this.service) >= 0 && this.region === 'us-east-1') return true\n\n  return ['cloudfront', 'ls', 'route53', 'iam', 'importexport', 'sts']\n    .indexOf(this.service) >= 0\n}\n\nRequestSigner.prototype.createHost = function() {\n  var region = this.isSingleRegion() ? '' : '.' + this.region,\n      subdomain = this.service === 'ses' ? 'email' : this.service\n  return subdomain + region + '.amazonaws.com'\n}\n\nRequestSigner.prototype.prepareRequest = function() {\n  this.parsePath()\n\n  var request = this.request, headers = request.headers, query\n\n  if (request.signQuery) {\n\n    this.parsedPath.query = query = this.parsedPath.query || {}\n\n    if (this.credentials.sessionToken)\n      query['X-Amz-Security-Token'] = this.credentials.sessionToken\n\n    if (this.service === 's3' && !query['X-Amz-Expires'])\n      query['X-Amz-Expires'] = 86400\n\n    if (query['X-Amz-Date'])\n      this.datetime = query['X-Amz-Date']\n    else\n      query['X-Amz-Date'] = this.getDateTime()\n\n    query['X-Amz-Algorithm'] = 'AWS4-HMAC-SHA256'\n    query['X-Amz-Credential'] = this.credentials.accessKeyId + '/' + this.credentialString()\n    query['X-Amz-SignedHeaders'] = this.signedHeaders()\n\n  } else {\n\n    if (!request.doNotModifyHeaders && !this.isCodeCommitGit) {\n      if (request.body && !headers['Content-Type'] && !headers['content-type'])\n        headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=utf-8'\n\n      if (request.body && !headers['Content-Length'] && !headers['content-length'])\n        headers['Content-Length'] = Buffer.byteLength(request.body)\n\n      if (this.credentials.sessionToken && !headers['X-Amz-Security-Token'] && !headers['x-amz-security-token'])\n        headers['X-Amz-Security-Token'] = this.credentials.sessionToken\n\n      if (this.service === 's3' && !headers['X-Amz-Content-Sha256'] && !headers['x-amz-content-sha256'])\n        headers['X-Amz-Content-Sha256'] = hash(this.request.body || '', 'hex')\n\n      if (headers['X-Amz-Date'] || headers['x-amz-date'])\n        this.datetime = headers['X-Amz-Date'] || headers['x-amz-date']\n      else\n        headers['X-Amz-Date'] = this.getDateTime()\n    }\n\n    delete headers.Authorization\n    delete headers.authorization\n  }\n}\n\nRequestSigner.prototype.sign = function() {\n  if (!this.parsedPath) this.prepareRequest()\n\n  if (this.request.signQuery) {\n    this.parsedPath.query['X-Amz-Signature'] = this.signature()\n  } else {\n    this.request.headers.Authorization = this.authHeader()\n  }\n\n  this.request.path = this.formatPath()\n\n  return this.request\n}\n\nRequestSigner.prototype.getDateTime = function() {\n  if (!this.datetime) {\n    var headers = this.request.headers,\n      date = new Date(headers.Date || headers.date || new Date)\n\n    this.datetime = date.toISOString().replace(/[:\\-]|\\.\\d{3}/g, '')\n\n    // Remove the trailing 'Z' on the timestamp string for CodeCommit git access\n    if (this.isCodeCommitGit) this.datetime = this.datetime.slice(0, -1)\n  }\n  return this.datetime\n}\n\nRequestSigner.prototype.getDate = function() {\n  return this.getDateTime().substr(0, 8)\n}\n\nRequestSigner.prototype.authHeader = function() {\n  return [\n    'AWS4-HMAC-SHA256 Credential=' + this.credentials.accessKeyId + '/' + this.credentialString(),\n    'SignedHeaders=' + this.signedHeaders(),\n    'Signature=' + this.signature(),\n  ].join(', ')\n}\n\nRequestSigner.prototype.signature = function() {\n  var date = this.getDate(),\n      cacheKey = [this.credentials.secretAccessKey, date, this.region, this.service].join(),\n      kDate, kRegion, kService, kCredentials = credentialsCache.get(cacheKey)\n  if (!kCredentials) {\n    kDate = hmac('AWS4' + this.credentials.secretAccessKey, date)\n    kRegion = hmac(kDate, this.region)\n    kService = hmac(kRegion, this.service)\n    kCredentials = hmac(kService, 'aws4_request')\n    credentialsCache.set(cacheKey, kCredentials)\n  }\n  return hmac(kCredentials, this.stringToSign(), 'hex')\n}\n\nRequestSigner.prototype.stringToSign = function() {\n  return [\n    'AWS4-HMAC-SHA256',\n    this.getDateTime(),\n    this.credentialString(),\n    hash(this.canonicalString(), 'hex'),\n  ].join('\\n')\n}\n\nRequestSigner.prototype.canonicalString = function() {\n  if (!this.parsedPath) this.prepareRequest()\n\n  var pathStr = this.parsedPath.path,\n      query = this.parsedPath.query,\n      headers = this.request.headers,\n      queryStr = '',\n      normalizePath = this.service !== 's3',\n      decodePath = this.service === 's3' || this.request.doNotEncodePath,\n      decodeSlashesInPath = this.service === 's3',\n      firstValOnly = this.service === 's3',\n      bodyHash\n\n  if (this.service === 's3' && this.request.signQuery) {\n    bodyHash = 'UNSIGNED-PAYLOAD'\n  } else if (this.isCodeCommitGit) {\n    bodyHash = ''\n  } else {\n    bodyHash = headers['X-Amz-Content-Sha256'] || headers['x-amz-content-sha256'] ||\n      hash(this.request.body || '', 'hex')\n  }\n\n  if (query) {\n    var reducedQuery = Object.keys(query).reduce(function(obj, key) {\n      if (!key) return obj\n      obj[encodeRfc3986Full(key)] = !Array.isArray(query[key]) ? query[key] :\n        (firstValOnly ? query[key][0] : query[key])\n      return obj\n    }, {})\n    var encodedQueryPieces = []\n    Object.keys(reducedQuery).sort().forEach(function(key) {\n      if (!Array.isArray(reducedQuery[key])) {\n        encodedQueryPieces.push(key + '=' + encodeRfc3986Full(reducedQuery[key]))\n      } else {\n        reducedQuery[key].map(encodeRfc3986Full).sort()\n          .forEach(function(val) { encodedQueryPieces.push(key + '=' + val) })\n      }\n    })\n    queryStr = encodedQueryPieces.join('&')\n  }\n  if (pathStr !== '/') {\n    if (normalizePath) pathStr = pathStr.replace(/\\/{2,}/g, '/')\n    pathStr = pathStr.split('/').reduce(function(path, piece) {\n      if (normalizePath && piece === '..') {\n        path.pop()\n      } else if (!normalizePath || piece !== '.') {\n        if (decodePath) piece = decodeURIComponent(piece.replace(/\\+/g, ' '))\n        path.push(encodeRfc3986Full(piece))\n      }\n      return path\n    }, []).join('/')\n    if (pathStr[0] !== '/') pathStr = '/' + pathStr\n    if (decodeSlashesInPath) pathStr = pathStr.replace(/%2F/g, '/')\n  }\n\n  return [\n    this.request.method || 'GET',\n    pathStr,\n    queryStr,\n    this.canonicalHeaders() + '\\n',\n    this.signedHeaders(),\n    bodyHash,\n  ].join('\\n')\n}\n\nRequestSigner.prototype.canonicalHeaders = function() {\n  var headers = this.request.headers\n  function trimAll(header) {\n    return header.toString().trim().replace(/\\s+/g, ' ')\n  }\n  return Object.keys(headers)\n    .filter(function(key) { return HEADERS_TO_IGNORE[key.toLowerCase()] == null })\n    .sort(function(a, b) { return a.toLowerCase() < b.toLowerCase() ? -1 : 1 })\n    .map(function(key) { return key.toLowerCase() + ':' + trimAll(headers[key]) })\n    .join('\\n')\n}\n\nRequestSigner.prototype.signedHeaders = function() {\n  var extraHeadersToInclude = this.extraHeadersToInclude,\n      extraHeadersToIgnore = this.extraHeadersToIgnore\n  return Object.keys(this.request.headers)\n    .map(function(key) { return key.toLowerCase() })\n    .filter(function(key) {\n      return extraHeadersToInclude[key] ||\n        (HEADERS_TO_IGNORE[key] == null && !extraHeadersToIgnore[key])\n    })\n    .sort()\n    .join(';')\n}\n\nRequestSigner.prototype.credentialString = function() {\n  return [\n    this.getDate(),\n    this.region,\n    this.service,\n    'aws4_request',\n  ].join('/')\n}\n\nRequestSigner.prototype.defaultCredentials = function() {\n  var env = process.env\n  return {\n    accessKeyId: env.AWS_ACCESS_KEY_ID || env.AWS_ACCESS_KEY,\n    secretAccessKey: env.AWS_SECRET_ACCESS_KEY || env.AWS_SECRET_KEY,\n    sessionToken: env.AWS_SESSION_TOKEN,\n  }\n}\n\nRequestSigner.prototype.parsePath = function() {\n  var path = this.request.path || '/'\n\n  // S3 doesn't always encode characters > 127 correctly and\n  // all services don't encode characters > 255 correctly\n  // So if there are non-reserved chars (and it's not already all % encoded), just encode them all\n  if (/[^0-9A-Za-z;,/?:@&=+$\\-_.!~*'()#%]/.test(path)) {\n    path = encodeURI(decodeURI(path))\n  }\n\n  var queryIx = path.indexOf('?'),\n      query = null\n\n  if (queryIx >= 0) {\n    query = querystring.parse(path.slice(queryIx + 1))\n    path = path.slice(0, queryIx)\n  }\n\n  this.parsedPath = {\n    path: path,\n    query: query,\n  }\n}\n\nRequestSigner.prototype.formatPath = function() {\n  var path = this.parsedPath.path,\n      query = this.parsedPath.query\n\n  if (!query) return path\n\n  // Services don't support empty query string keys\n  if (query[''] != null) delete query['']\n\n  return path + '?' + encodeRfc3986(querystring.stringify(query))\n}\n\naws4.RequestSigner = RequestSigner\n\naws4.sign = function(request, credentials) {\n  return new RequestSigner(request, credentials).sign()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/aws4/aws4.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/aws4/lru.js":
/*!**********************************!*\
  !*** ./node_modules/aws4/lru.js ***!
  \**********************************/
/***/ ((module) => {

eval("module.exports = function(size) {\n  return new LruCache(size)\n}\n\nfunction LruCache(size) {\n  this.capacity = size | 0\n  this.map = Object.create(null)\n  this.list = new DoublyLinkedList()\n}\n\nLruCache.prototype.get = function(key) {\n  var node = this.map[key]\n  if (node == null) return undefined\n  this.used(node)\n  return node.val\n}\n\nLruCache.prototype.set = function(key, val) {\n  var node = this.map[key]\n  if (node != null) {\n    node.val = val\n  } else {\n    if (!this.capacity) this.prune()\n    if (!this.capacity) return false\n    node = new DoublyLinkedNode(key, val)\n    this.map[key] = node\n    this.capacity--\n  }\n  this.used(node)\n  return true\n}\n\nLruCache.prototype.used = function(node) {\n  this.list.moveToFront(node)\n}\n\nLruCache.prototype.prune = function() {\n  var node = this.list.pop()\n  if (node != null) {\n    delete this.map[node.key]\n    this.capacity++\n  }\n}\n\n\nfunction DoublyLinkedList() {\n  this.firstNode = null\n  this.lastNode = null\n}\n\nDoublyLinkedList.prototype.moveToFront = function(node) {\n  if (this.firstNode == node) return\n\n  this.remove(node)\n\n  if (this.firstNode == null) {\n    this.firstNode = node\n    this.lastNode = node\n    node.prev = null\n    node.next = null\n  } else {\n    node.prev = null\n    node.next = this.firstNode\n    node.next.prev = node\n    this.firstNode = node\n  }\n}\n\nDoublyLinkedList.prototype.pop = function() {\n  var lastNode = this.lastNode\n  if (lastNode != null) {\n    this.remove(lastNode)\n  }\n  return lastNode\n}\n\nDoublyLinkedList.prototype.remove = function(node) {\n  if (this.firstNode == node) {\n    this.firstNode = node.next\n  } else if (node.prev != null) {\n    node.prev.next = node.next\n  }\n  if (this.lastNode == node) {\n    this.lastNode = node.prev\n  } else if (node.next != null) {\n    node.next.prev = node.prev\n  }\n}\n\n\nfunction DoublyLinkedNode(key, val) {\n  this.key = key\n  this.val = val\n  this.prev = null\n  this.next = null\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/aws4/lru.js\n");

/***/ })

};
;