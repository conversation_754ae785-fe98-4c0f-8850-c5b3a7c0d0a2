import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader) {
      return NextResponse.json(
        { error: "Authorization header required" },
        { status: 401 }
      );
    }

    const { planId, durationId, couponCode } = await request.json();
    if (!planId || !durationId) {
      return NextResponse.json(
        { error: "planId and durationId are required" },
        { status: 400 }
      );
    }

    const apiUrl = `${process.env.API_BASE_URL}/api/payments/create-order`;
    console.log("Calling backend API:", apiUrl);

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: authHeader,
      },
      body: JSON.stringify({
        planId,
        durationId,
        couponCode,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      let errorMessage = data.message || "Failed to create order";

      // Handle specific error cases
      if (response.status === 400) {
        if (errorMessage.includes("User already has an active subscription")) {
          errorMessage =
            "You already have an active subscription. Please contact support if you need to upgrade.";
        } else if (errorMessage.includes("Plan not found")) {
          errorMessage =
            "The selected plan is no longer available. Please choose a different plan.";
        } else if (errorMessage.includes("Duration option not found")) {
          errorMessage =
            "The selected duration is no longer available. Please choose a different duration.";
        }
      }
      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error("Error creating order:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
