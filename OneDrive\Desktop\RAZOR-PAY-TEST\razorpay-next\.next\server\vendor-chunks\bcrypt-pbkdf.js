"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcrypt-pbkdf";
exports.ids = ["vendor-chunks/bcrypt-pbkdf"];
exports.modules = {

/***/ "(rsc)/./node_modules/bcrypt-pbkdf/index.js":
/*!********************************************!*\
  !*** ./node_modules/bcrypt-pbkdf/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar crypto_hash_sha512 = (__webpack_require__(/*! tweetnacl */ \"(rsc)/./node_modules/tweetnacl/nacl-fast.js\").lowlevel.crypto_hash);\n\n/*\n * This file is a 1:1 port from the OpenBSD blowfish.c and bcrypt_pbkdf.c. As a\n * result, it retains the original copyright and license. The two files are\n * under slightly different (but compatible) licenses, and are here combined in\n * one file.\n *\n * Credit for the actual porting work goes to:\n *  Devi Mandiri <<EMAIL>>\n */\n\n/*\n * The Blowfish portions are under the following license:\n *\n * Blowfish block cipher for OpenBSD\n * Copyright 1997 Niels Provos <<EMAIL>>\n * All rights reserved.\n *\n * Implementation advice by David Mazieres <<EMAIL>>.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions\n * are met:\n * 1. Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n * 2. Redistributions in binary form must reproduce the above copyright\n *    notice, this list of conditions and the following disclaimer in the\n *    documentation and/or other materials provided with the distribution.\n * 3. The name of the author may not be used to endorse or promote products\n *    derived from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n/*\n * The bcrypt_pbkdf portions are under the following license:\n *\n * Copyright (c) 2013 Ted Unangst <<EMAIL>>\n *\n * Permission to use, copy, modify, and distribute this software for any\n * purpose with or without fee is hereby granted, provided that the above\n * copyright notice and this permission notice appear in all copies.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n */\n\n/*\n * Performance improvements (Javascript-specific):\n *\n * Copyright 2016, Joyent Inc\n * Author: Alex Wilson <<EMAIL>>\n *\n * Permission to use, copy, modify, and distribute this software for any\n * purpose with or without fee is hereby granted, provided that the above\n * copyright notice and this permission notice appear in all copies.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n */\n\n// Ported from OpenBSD bcrypt_pbkdf.c v1.9\n\nvar BLF_J = 0;\n\nvar Blowfish = function() {\n  this.S = [\n    new Uint32Array([\n      0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7,\n      0xb8e1afed, 0x6a267e96, 0xba7c9045, 0xf12c7f99,\n      0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n      0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e,\n      0x0d95748f, 0x728eb658, 0x718bcd58, 0x82154aee,\n      0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n      0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef,\n      0x8e79dcb0, 0x603a180e, 0x6c9e0e8b, 0xb01e8a3e,\n      0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n      0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440,\n      0x55ca396a, 0x2aab10b6, 0xb4cc5c34, 0x1141e8ce,\n      0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n      0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e,\n      0xafd6ba33, 0x6c24cf5c, 0x7a325381, 0x28958677,\n      0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n      0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032,\n      0xef845d5d, 0xe98575b1, 0xdc262302, 0xeb651b88,\n      0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n      0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e,\n      0x21c66842, 0xf6e96c9a, 0x670c9c61, 0xabd388f0,\n      0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n      0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98,\n      0xa1f1651d, 0x39af0176, 0x66ca593e, 0x82430e88,\n      0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n      0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6,\n      0x4ed3aa62, 0x363f7706, 0x1bfedf72, 0x429b023d,\n      0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n      0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7,\n      0xe3fe501a, 0xb6794c3b, 0x976ce0bd, 0x04c006ba,\n      0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n      0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f,\n      0x6dfc511f, 0x9b30952c, 0xcc814544, 0xaf5ebd09,\n      0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n      0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb,\n      0x5579c0bd, 0x1a60320a, 0xd6a100c6, 0x402c7279,\n      0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n      0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab,\n      0x323db5fa, 0xfd238760, 0x53317b48, 0x3e00df82,\n      0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n      0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573,\n      0x695b27b0, 0xbbca58c8, 0xe1ffa35d, 0xb8f011a0,\n      0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n      0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790,\n      0xe1ddf2da, 0xa4cb7e33, 0x62fb1341, 0xcee4c6e8,\n      0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n      0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0,\n      0xd08ed1d0, 0xafc725e0, 0x8e3c5b2f, 0x8e7594b7,\n      0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n      0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad,\n      0x2f2f2218, 0xbe0e1777, 0xea752dfe, 0x8b021fa1,\n      0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n      0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9,\n      0x165fa266, 0x80957705, 0x93cc7314, 0x211a1477,\n      0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n      0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49,\n      0x00250e2d, 0x2071b35e, 0x226800bb, 0x57b8e0af,\n      0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n      0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5,\n      0x83260376, 0x6295cfa9, 0x11c81968, 0x4e734a41,\n      0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n      0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400,\n      0x08ba6fb5, 0x571be91f, 0xf296ec6b, 0x2a0dd915,\n      0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n      0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a]),\n    new Uint32Array([\n      0x4b7a70e9, 0xb5b32944, 0xdb75092e, 0xc4192623,\n      0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n      0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1,\n      0x193602a5, 0x75094c29, 0xa0591340, 0xe4183a3e,\n      0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n      0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1,\n      0x4cdd2086, 0x8470eb26, 0x6382e9c6, 0x021ecc5e,\n      0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n      0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737,\n      0x3e07841c, 0x7fdeae5c, 0x8e7d44ec, 0x5716f2b8,\n      0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n      0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd,\n      0xd19113f9, 0x7ca92ff6, 0x94324773, 0x22f54701,\n      0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n      0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41,\n      0xe238cd99, 0x3bea0e2f, 0x3280bba1, 0x183eb331,\n      0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n      0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af,\n      0xde9a771f, 0xd9930810, 0xb38bae12, 0xdccf3f2e,\n      0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n      0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c,\n      0xec7aec3a, 0xdb851dfa, 0x63094366, 0xc464c3d2,\n      0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n      0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd,\n      0x71dff89e, 0x10314e55, 0x81ac77d6, 0x5f11199b,\n      0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n      0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e,\n      0x86e34570, 0xeae96fb1, 0x860e5e0a, 0x5a3e2ab3,\n      0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n      0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a,\n      0xc6150eba, 0x94e2ea78, 0xa5fc3c53, 0x1e0a2df4,\n      0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n      0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66,\n      0xe3bc4595, 0xa67bc883, 0xb17f37d1, 0x018cff28,\n      0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n      0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84,\n      0x1521b628, 0x29076170, 0xecdd4775, 0x619f1510,\n      0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n      0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14,\n      0xeecc86bc, 0x60622ca7, 0x9cab5cab, 0xb2f3846e,\n      0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n      0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7,\n      0x9b540b19, 0x875fa099, 0x95f7997e, 0x623d7da8,\n      0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n      0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99,\n      0x57f584a5, 0x1b227263, 0x9b83c3ff, 0x1ac24696,\n      0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n      0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73,\n      0x5d4a14d9, 0xe864b7e3, 0x42105d14, 0x203e13e0,\n      0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n      0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105,\n      0xd81e799e, 0x86854dc7, 0xe44b476a, 0x3d816250,\n      0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n      0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285,\n      0x095bbf00, 0xad19489d, 0x1462b174, 0x23820e00,\n      0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n      0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb,\n      0x7cde3759, 0xcbee7460, 0x4085f2a7, 0xce77326e,\n      0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n      0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc,\n      0x9e447a2e, 0xc3453484, 0xfdd56705, 0x0e1e9ec9,\n      0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n      0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20,\n      0x153e21e7, 0x8fb03d4a, 0xe6e39f2b, 0xdb83adf7]),\n    new Uint32Array([\n      0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n      0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068,\n      0xd4082471, 0x3320f46a, 0x43b7d4b7, 0x500061af,\n      0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n      0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45,\n      0xbfbc09ec, 0x03bd9785, 0x7fac6dd0, 0x31cb8504,\n      0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n      0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb,\n      0x68dc1462, 0xd7486900, 0x680ec0a4, 0x27a18dee,\n      0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n      0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42,\n      0x20fe9e35, 0xd9f385b9, 0xee39d7ab, 0x3b124e8b,\n      0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n      0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb,\n      0xfb0af54e, 0xd8feb397, 0x454056ac, 0xba489527,\n      0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n      0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33,\n      0xa62a4a56, 0x3f3125f9, 0x5ef47e1c, 0x9029317c,\n      0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n      0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc,\n      0x07f9c9ee, 0x41041f0f, 0x404779a4, 0x5d886e17,\n      0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n      0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b,\n      0x0e12b4c2, 0x02e1329e, 0xaf664fd1, 0xcad18115,\n      0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n      0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728,\n      0xd0127845, 0x95b794fd, 0x647d0862, 0xe7ccf5f0,\n      0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n      0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37,\n      0xa812dc60, 0xa1ebddf8, 0x991be14c, 0xdb6e6b0d,\n      0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n      0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b,\n      0x667b9ffb, 0xcedb7d9c, 0xa091cf0b, 0xd9155ea3,\n      0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n      0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d,\n      0x6842ada7, 0xc66a2b3b, 0x12754ccc, 0x782ef11c,\n      0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n      0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9,\n      0x44421659, 0x0a121386, 0xd90cec6e, 0xd5abea2a,\n      0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n      0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d,\n      0xd1fd8346, 0xf6381fb0, 0x7745ae04, 0xd736fccc,\n      0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n      0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61,\n      0x4e58f48f, 0xf2ddfda2, 0xf474ef38, 0x8789bdc2,\n      0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n      0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2,\n      0x466e598e, 0x20b45770, 0x8cd55591, 0xc902de4c,\n      0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n      0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633,\n      0xe85a1f02, 0x09f0be8c, 0x4a99a025, 0x1d6efe10,\n      0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n      0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52,\n      0x50115e01, 0xa70683fa, 0xa002b5c4, 0x0de6d027,\n      0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n      0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62,\n      0x11e69ed7, 0x2338ea63, 0x53c2dd94, 0xc2c21634,\n      0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n      0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24,\n      0x86e3725f, 0x724d9db9, 0x1ac15bb4, 0xd39eb8fc,\n      0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n      0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c,\n      0x6fd5c7e7, 0x56e14ec4, 0x362abfce, 0xddc6c837,\n      0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0]),\n    new Uint32Array([\n      0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b,\n      0x5cb0679e, 0x4fa33742, 0xd3822740, 0x99bc9bbe,\n      0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n      0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4,\n      0x5748ab2f, 0xbc946e79, 0xc6a376d2, 0x6549c2c8,\n      0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n      0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304,\n      0xa1fad5f0, 0x6a2d519a, 0x63ef8ce2, 0x9a86ee22,\n      0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n      0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6,\n      0x2826a2f9, 0xa73a3ae1, 0x4ba99586, 0xef5562e9,\n      0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n      0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593,\n      0xe990fd5a, 0x9e34d797, 0x2cf0b7d9, 0x022b8b51,\n      0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n      0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c,\n      0xe029ac71, 0xe019a5e6, 0x47b0acfd, 0xed93fa9b,\n      0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n      0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c,\n      0x15056dd4, 0x88f46dba, 0x03a16125, 0x0564f0bd,\n      0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n      0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319,\n      0x7533d928, 0xb155fdf5, 0x03563482, 0x8aba3cbb,\n      0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n      0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991,\n      0xea7a90c2, 0xfb3e7bce, 0x5121ce64, 0x774fbe32,\n      0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n      0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166,\n      0xb39a460a, 0x6445c0dd, 0x586cdecf, 0x1c20c8ae,\n      0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n      0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5,\n      0x72eacea8, 0xfa6484bb, 0x8d6612ae, 0xbf3c6f47,\n      0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n      0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d,\n      0x4040cb08, 0x4eb4e2cc, 0x34d2466a, 0x0115af84,\n      0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n      0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8,\n      0x611560b1, 0xe7933fdc, 0xbb3a792b, 0x344525bd,\n      0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n      0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7,\n      0x1a908749, 0xd44fbd9a, 0xd0dadecb, 0xd50ada38,\n      0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n      0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c,\n      0xbf97222c, 0x15e6fc2a, 0x0f91fc71, 0x9b941525,\n      0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n      0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442,\n      0xe0ec6e0e, 0x1698db3b, 0x4c98a0be, 0x3278e964,\n      0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n      0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8,\n      0xdf359f8d, 0x9b992f2e, 0xe60b6f47, 0x0fe3f11d,\n      0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n      0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299,\n      0xf523f357, 0xa6327623, 0x93a83531, 0x56cccd02,\n      0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n      0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614,\n      0xe6c6c7bd, 0x327a140a, 0x45e1d006, 0xc3f27b9a,\n      0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n      0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b,\n      0x53113ec0, 0x1640e3d3, 0x38abbd60, 0x2547adf0,\n      0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n      0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e,\n      0x1948c25c, 0x02fb8a8c, 0x01c36ae4, 0xd6ebe1f9,\n      0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n      0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6])\n    ];\n  this.P = new Uint32Array([\n    0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344,\n    0xa4093822, 0x299f31d0, 0x082efa98, 0xec4e6c89,\n    0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n    0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917,\n    0x9216d5d9, 0x8979fb1b]);\n};\n\nfunction F(S, x8, i) {\n  return (((S[0][x8[i+3]] +\n            S[1][x8[i+2]]) ^\n            S[2][x8[i+1]]) +\n            S[3][x8[i]]);\n};\n\nBlowfish.prototype.encipher = function(x, x8) {\n  if (x8 === undefined) {\n    x8 = new Uint8Array(x.buffer);\n    if (x.byteOffset !== 0)\n      x8 = x8.subarray(x.byteOffset);\n  }\n  x[0] ^= this.P[0];\n  for (var i = 1; i < 16; i += 2) {\n    x[1] ^= F(this.S, x8, 0) ^ this.P[i];\n    x[0] ^= F(this.S, x8, 4) ^ this.P[i+1];\n  }\n  var t = x[0];\n  x[0] = x[1] ^ this.P[17];\n  x[1] = t;\n};\n\nBlowfish.prototype.decipher = function(x) {\n  var x8 = new Uint8Array(x.buffer);\n  if (x.byteOffset !== 0)\n    x8 = x8.subarray(x.byteOffset);\n  x[0] ^= this.P[17];\n  for (var i = 16; i > 0; i -= 2) {\n    x[1] ^= F(this.S, x8, 0) ^ this.P[i];\n    x[0] ^= F(this.S, x8, 4) ^ this.P[i-1];\n  }\n  var t = x[0];\n  x[0] = x[1] ^ this.P[0];\n  x[1] = t;\n};\n\nfunction stream2word(data, databytes){\n  var i, temp = 0;\n  for (i = 0; i < 4; i++, BLF_J++) {\n    if (BLF_J >= databytes) BLF_J = 0;\n    temp = (temp << 8) | data[BLF_J];\n  }\n  return temp;\n};\n\nBlowfish.prototype.expand0state = function(key, keybytes) {\n  var d = new Uint32Array(2), i, k;\n  var d8 = new Uint8Array(d.buffer);\n\n  for (i = 0, BLF_J = 0; i < 18; i++) {\n    this.P[i] ^= stream2word(key, keybytes);\n  }\n  BLF_J = 0;\n\n  for (i = 0; i < 18; i += 2) {\n    this.encipher(d, d8);\n    this.P[i]   = d[0];\n    this.P[i+1] = d[1];\n  }\n\n  for (i = 0; i < 4; i++) {\n    for (k = 0; k < 256; k += 2) {\n      this.encipher(d, d8);\n      this.S[i][k]   = d[0];\n      this.S[i][k+1] = d[1];\n    }\n  }\n};\n\nBlowfish.prototype.expandstate = function(data, databytes, key, keybytes) {\n  var d = new Uint32Array(2), i, k;\n\n  for (i = 0, BLF_J = 0; i < 18; i++) {\n    this.P[i] ^= stream2word(key, keybytes);\n  }\n\n  for (i = 0, BLF_J = 0; i < 18; i += 2) {\n    d[0] ^= stream2word(data, databytes);\n    d[1] ^= stream2word(data, databytes);\n    this.encipher(d);\n    this.P[i]   = d[0];\n    this.P[i+1] = d[1];\n  }\n\n  for (i = 0; i < 4; i++) {\n    for (k = 0; k < 256; k += 2) {\n      d[0] ^= stream2word(data, databytes);\n      d[1] ^= stream2word(data, databytes);\n      this.encipher(d);\n      this.S[i][k]   = d[0];\n      this.S[i][k+1] = d[1];\n    }\n  }\n  BLF_J = 0;\n};\n\nBlowfish.prototype.enc = function(data, blocks) {\n  for (var i = 0; i < blocks; i++) {\n    this.encipher(data.subarray(i*2));\n  }\n};\n\nBlowfish.prototype.dec = function(data, blocks) {\n  for (var i = 0; i < blocks; i++) {\n    this.decipher(data.subarray(i*2));\n  }\n};\n\nvar BCRYPT_BLOCKS = 8,\n    BCRYPT_HASHSIZE = 32;\n\nfunction bcrypt_hash(sha2pass, sha2salt, out) {\n  var state = new Blowfish(),\n      cdata = new Uint32Array(BCRYPT_BLOCKS), i,\n      ciphertext = new Uint8Array([79,120,121,99,104,114,111,109,97,116,105,\n            99,66,108,111,119,102,105,115,104,83,119,97,116,68,121,110,97,109,\n            105,116,101]); //\"OxychromaticBlowfishSwatDynamite\"\n\n  state.expandstate(sha2salt, 64, sha2pass, 64);\n  for (i = 0; i < 64; i++) {\n    state.expand0state(sha2salt, 64);\n    state.expand0state(sha2pass, 64);\n  }\n\n  for (i = 0; i < BCRYPT_BLOCKS; i++)\n    cdata[i] = stream2word(ciphertext, ciphertext.byteLength);\n  for (i = 0; i < 64; i++)\n    state.enc(cdata, cdata.byteLength / 8);\n\n  for (i = 0; i < BCRYPT_BLOCKS; i++) {\n    out[4*i+3] = cdata[i] >>> 24;\n    out[4*i+2] = cdata[i] >>> 16;\n    out[4*i+1] = cdata[i] >>> 8;\n    out[4*i+0] = cdata[i];\n  }\n};\n\nfunction bcrypt_pbkdf(pass, passlen, salt, saltlen, key, keylen, rounds) {\n  var sha2pass = new Uint8Array(64),\n      sha2salt = new Uint8Array(64),\n      out = new Uint8Array(BCRYPT_HASHSIZE),\n      tmpout = new Uint8Array(BCRYPT_HASHSIZE),\n      countsalt = new Uint8Array(saltlen+4),\n      i, j, amt, stride, dest, count,\n      origkeylen = keylen;\n\n  if (rounds < 1)\n    return -1;\n  if (passlen === 0 || saltlen === 0 || keylen === 0 ||\n      keylen > (out.byteLength * out.byteLength) || saltlen > (1<<20))\n    return -1;\n\n  stride = Math.floor((keylen + out.byteLength - 1) / out.byteLength);\n  amt = Math.floor((keylen + stride - 1) / stride);\n\n  for (i = 0; i < saltlen; i++)\n    countsalt[i] = salt[i];\n\n  crypto_hash_sha512(sha2pass, pass, passlen);\n\n  for (count = 1; keylen > 0; count++) {\n    countsalt[saltlen+0] = count >>> 24;\n    countsalt[saltlen+1] = count >>> 16;\n    countsalt[saltlen+2] = count >>>  8;\n    countsalt[saltlen+3] = count;\n\n    crypto_hash_sha512(sha2salt, countsalt, saltlen + 4);\n    bcrypt_hash(sha2pass, sha2salt, tmpout);\n    for (i = out.byteLength; i--;)\n      out[i] = tmpout[i];\n\n    for (i = 1; i < rounds; i++) {\n      crypto_hash_sha512(sha2salt, tmpout, tmpout.byteLength);\n      bcrypt_hash(sha2pass, sha2salt, tmpout);\n      for (j = 0; j < out.byteLength; j++)\n        out[j] ^= tmpout[j];\n    }\n\n    amt = Math.min(amt, keylen);\n    for (i = 0; i < amt; i++) {\n      dest = i * stride + (count - 1);\n      if (dest >= origkeylen)\n        break;\n      key[dest] = out[i];\n    }\n    keylen -= i;\n  }\n\n  return 0;\n};\n\nmodule.exports = {\n      BLOCKS: BCRYPT_BLOCKS,\n      HASHSIZE: BCRYPT_HASHSIZE,\n      hash: bcrypt_hash,\n      pbkdf: bcrypt_pbkdf\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcrypt-pbkdf/index.js\n");

/***/ })

};
;