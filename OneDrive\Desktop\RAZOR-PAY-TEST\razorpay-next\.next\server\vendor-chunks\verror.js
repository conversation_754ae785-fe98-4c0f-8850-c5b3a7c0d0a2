/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/verror";
exports.ids = ["vendor-chunks/verror"];
exports.modules = {

/***/ "(rsc)/./node_modules/verror/lib/verror.js":
/*!*******************************************!*\
  !*** ./node_modules/verror/lib/verror.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*\n * verror.js: richer JavaScript errors\n */\n\nvar mod_assertplus = __webpack_require__(/*! assert-plus */ \"(rsc)/./node_modules/assert-plus/assert.js\");\nvar mod_util = __webpack_require__(/*! util */ \"util\");\n\nvar mod_extsprintf = __webpack_require__(/*! extsprintf */ \"(rsc)/./node_modules/extsprintf/lib/extsprintf.js\");\nvar mod_isError = (__webpack_require__(/*! core-util-is */ \"(rsc)/./node_modules/core-util-is/lib/util.js\").isError);\nvar sprintf = mod_extsprintf.sprintf;\n\n/*\n * Public interface\n */\n\n/* So you can 'var VError = require('verror')' */\nmodule.exports = VError;\n/* For compatibility */\nVError.VError = VError;\n/* Other exported classes */\nVError.SError = SError;\nVError.WError = WError;\nVError.MultiError = MultiError;\n\n/*\n * Common function used to parse constructor arguments for VError, WError, and\n * SError.  Named arguments to this function:\n *\n *     strict\t\tforce strict interpretation of sprintf arguments, even\n *     \t\t\tif the options in \"argv\" don't say so\n *\n *     argv\t\terror's constructor arguments, which are to be\n *     \t\t\tinterpreted as described in README.md.  For quick\n *     \t\t\treference, \"argv\" has one of the following forms:\n *\n *          [ sprintf_args... ]           (argv[0] is a string)\n *          [ cause, sprintf_args... ]    (argv[0] is an Error)\n *          [ options, sprintf_args... ]  (argv[0] is an object)\n *\n * This function normalizes these forms, producing an object with the following\n * properties:\n *\n *    options           equivalent to \"options\" in third form.  This will never\n *    \t\t\tbe a direct reference to what the caller passed in\n *    \t\t\t(i.e., it may be a shallow copy), so it can be freely\n *    \t\t\tmodified.\n *\n *    shortmessage      result of sprintf(sprintf_args), taking options.strict\n *    \t\t\tinto account as described in README.md.\n */\nfunction parseConstructorArguments(args)\n{\n\tvar argv, options, sprintf_args, shortmessage, k;\n\n\tmod_assertplus.object(args, 'args');\n\tmod_assertplus.bool(args.strict, 'args.strict');\n\tmod_assertplus.array(args.argv, 'args.argv');\n\targv = args.argv;\n\n\t/*\n\t * First, figure out which form of invocation we've been given.\n\t */\n\tif (argv.length === 0) {\n\t\toptions = {};\n\t\tsprintf_args = [];\n\t} else if (mod_isError(argv[0])) {\n\t\toptions = { 'cause': argv[0] };\n\t\tsprintf_args = argv.slice(1);\n\t} else if (typeof (argv[0]) === 'object') {\n\t\toptions = {};\n\t\tfor (k in argv[0]) {\n\t\t\toptions[k] = argv[0][k];\n\t\t}\n\t\tsprintf_args = argv.slice(1);\n\t} else {\n\t\tmod_assertplus.string(argv[0],\n\t\t    'first argument to VError, SError, or WError ' +\n\t\t    'constructor must be a string, object, or Error');\n\t\toptions = {};\n\t\tsprintf_args = argv;\n\t}\n\n\t/*\n\t * Now construct the error's message.\n\t *\n\t * extsprintf (which we invoke here with our caller's arguments in order\n\t * to construct this Error's message) is strict in its interpretation of\n\t * values to be processed by the \"%s\" specifier.  The value passed to\n\t * extsprintf must actually be a string or something convertible to a\n\t * String using .toString().  Passing other values (notably \"null\" and\n\t * \"undefined\") is considered a programmer error.  The assumption is\n\t * that if you actually want to print the string \"null\" or \"undefined\",\n\t * then that's easy to do that when you're calling extsprintf; on the\n\t * other hand, if you did NOT want that (i.e., there's actually a bug\n\t * where the program assumes some variable is non-null and tries to\n\t * print it, which might happen when constructing a packet or file in\n\t * some specific format), then it's better to stop immediately than\n\t * produce bogus output.\n\t *\n\t * However, sometimes the bug is only in the code calling VError, and a\n\t * programmer might prefer to have the error message contain \"null\" or\n\t * \"undefined\" rather than have the bug in the error path crash the\n\t * program (making the first bug harder to identify).  For that reason,\n\t * by default VError converts \"null\" or \"undefined\" arguments to their\n\t * string representations and passes those to extsprintf.  Programmers\n\t * desiring the strict behavior can use the SError class or pass the\n\t * \"strict\" option to the VError constructor.\n\t */\n\tmod_assertplus.object(options);\n\tif (!options.strict && !args.strict) {\n\t\tsprintf_args = sprintf_args.map(function (a) {\n\t\t\treturn (a === null ? 'null' :\n\t\t\t    a === undefined ? 'undefined' : a);\n\t\t});\n\t}\n\n\tif (sprintf_args.length === 0) {\n\t\tshortmessage = '';\n\t} else {\n\t\tshortmessage = sprintf.apply(null, sprintf_args);\n\t}\n\n\treturn ({\n\t    'options': options,\n\t    'shortmessage': shortmessage\n\t});\n}\n\n/*\n * See README.md for reference documentation.\n */\nfunction VError()\n{\n\tvar args, obj, parsed, cause, ctor, message, k;\n\n\targs = Array.prototype.slice.call(arguments, 0);\n\n\t/*\n\t * This is a regrettable pattern, but JavaScript's built-in Error class\n\t * is defined to work this way, so we allow the constructor to be called\n\t * without \"new\".\n\t */\n\tif (!(this instanceof VError)) {\n\t\tobj = Object.create(VError.prototype);\n\t\tVError.apply(obj, arguments);\n\t\treturn (obj);\n\t}\n\n\t/*\n\t * For convenience and backwards compatibility, we support several\n\t * different calling forms.  Normalize them here.\n\t */\n\tparsed = parseConstructorArguments({\n\t    'argv': args,\n\t    'strict': false\n\t});\n\n\t/*\n\t * If we've been given a name, apply it now.\n\t */\n\tif (parsed.options.name) {\n\t\tmod_assertplus.string(parsed.options.name,\n\t\t    'error\\'s \"name\" must be a string');\n\t\tthis.name = parsed.options.name;\n\t}\n\n\t/*\n\t * For debugging, we keep track of the original short message (attached\n\t * this Error particularly) separately from the complete message (which\n\t * includes the messages of our cause chain).\n\t */\n\tthis.jse_shortmsg = parsed.shortmessage;\n\tmessage = parsed.shortmessage;\n\n\t/*\n\t * If we've been given a cause, record a reference to it and update our\n\t * message appropriately.\n\t */\n\tcause = parsed.options.cause;\n\tif (cause) {\n\t\tmod_assertplus.ok(mod_isError(cause), 'cause is not an Error');\n\t\tthis.jse_cause = cause;\n\n\t\tif (!parsed.options.skipCauseMessage) {\n\t\t\tmessage += ': ' + cause.message;\n\t\t}\n\t}\n\n\t/*\n\t * If we've been given an object with properties, shallow-copy that\n\t * here.  We don't want to use a deep copy in case there are non-plain\n\t * objects here, but we don't want to use the original object in case\n\t * the caller modifies it later.\n\t */\n\tthis.jse_info = {};\n\tif (parsed.options.info) {\n\t\tfor (k in parsed.options.info) {\n\t\t\tthis.jse_info[k] = parsed.options.info[k];\n\t\t}\n\t}\n\n\tthis.message = message;\n\tError.call(this, message);\n\n\tif (Error.captureStackTrace) {\n\t\tctor = parsed.options.constructorOpt || this.constructor;\n\t\tError.captureStackTrace(this, ctor);\n\t}\n\n\treturn (this);\n}\n\nmod_util.inherits(VError, Error);\nVError.prototype.name = 'VError';\n\nVError.prototype.toString = function ve_toString()\n{\n\tvar str = (this.hasOwnProperty('name') && this.name ||\n\t\tthis.constructor.name || this.constructor.prototype.name);\n\tif (this.message)\n\t\tstr += ': ' + this.message;\n\n\treturn (str);\n};\n\n/*\n * This method is provided for compatibility.  New callers should use\n * VError.cause() instead.  That method also uses the saner `null` return value\n * when there is no cause.\n */\nVError.prototype.cause = function ve_cause()\n{\n\tvar cause = VError.cause(this);\n\treturn (cause === null ? undefined : cause);\n};\n\n/*\n * Static methods\n *\n * These class-level methods are provided so that callers can use them on\n * instances of Errors that are not VErrors.  New interfaces should be provided\n * only using static methods to eliminate the class of programming mistake where\n * people fail to check whether the Error object has the corresponding methods.\n */\n\nVError.cause = function (err)\n{\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\treturn (mod_isError(err.jse_cause) ? err.jse_cause : null);\n};\n\nVError.info = function (err)\n{\n\tvar rv, cause, k;\n\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\tcause = VError.cause(err);\n\tif (cause !== null) {\n\t\trv = VError.info(cause);\n\t} else {\n\t\trv = {};\n\t}\n\n\tif (typeof (err.jse_info) == 'object' && err.jse_info !== null) {\n\t\tfor (k in err.jse_info) {\n\t\t\trv[k] = err.jse_info[k];\n\t\t}\n\t}\n\n\treturn (rv);\n};\n\nVError.findCauseByName = function (err, name)\n{\n\tvar cause;\n\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\tmod_assertplus.string(name, 'name');\n\tmod_assertplus.ok(name.length > 0, 'name cannot be empty');\n\n\tfor (cause = err; cause !== null; cause = VError.cause(cause)) {\n\t\tmod_assertplus.ok(mod_isError(cause));\n\t\tif (cause.name == name) {\n\t\t\treturn (cause);\n\t\t}\n\t}\n\n\treturn (null);\n};\n\nVError.hasCauseWithName = function (err, name)\n{\n\treturn (VError.findCauseByName(err, name) !== null);\n};\n\nVError.fullStack = function (err)\n{\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\n\tvar cause = VError.cause(err);\n\n\tif (cause) {\n\t\treturn (err.stack + '\\ncaused by: ' + VError.fullStack(cause));\n\t}\n\n\treturn (err.stack);\n};\n\nVError.errorFromList = function (errors)\n{\n\tmod_assertplus.arrayOfObject(errors, 'errors');\n\n\tif (errors.length === 0) {\n\t\treturn (null);\n\t}\n\n\terrors.forEach(function (e) {\n\t\tmod_assertplus.ok(mod_isError(e));\n\t});\n\n\tif (errors.length == 1) {\n\t\treturn (errors[0]);\n\t}\n\n\treturn (new MultiError(errors));\n};\n\nVError.errorForEach = function (err, func)\n{\n\tmod_assertplus.ok(mod_isError(err), 'err must be an Error');\n\tmod_assertplus.func(func, 'func');\n\n\tif (err instanceof MultiError) {\n\t\terr.errors().forEach(function iterError(e) { func(e); });\n\t} else {\n\t\tfunc(err);\n\t}\n};\n\n\n/*\n * SError is like VError, but stricter about types.  You cannot pass \"null\" or\n * \"undefined\" as string arguments to the formatter.\n */\nfunction SError()\n{\n\tvar args, obj, parsed, options;\n\n\targs = Array.prototype.slice.call(arguments, 0);\n\tif (!(this instanceof SError)) {\n\t\tobj = Object.create(SError.prototype);\n\t\tSError.apply(obj, arguments);\n\t\treturn (obj);\n\t}\n\n\tparsed = parseConstructorArguments({\n\t    'argv': args,\n\t    'strict': true\n\t});\n\n\toptions = parsed.options;\n\tVError.call(this, options, '%s', parsed.shortmessage);\n\n\treturn (this);\n}\n\n/*\n * We don't bother setting SError.prototype.name because once constructed,\n * SErrors are just like VErrors.\n */\nmod_util.inherits(SError, VError);\n\n\n/*\n * Represents a collection of errors for the purpose of consumers that generally\n * only deal with one error.  Callers can extract the individual errors\n * contained in this object, but may also just treat it as a normal single\n * error, in which case a summary message will be printed.\n */\nfunction MultiError(errors)\n{\n\tmod_assertplus.array(errors, 'list of errors');\n\tmod_assertplus.ok(errors.length > 0, 'must be at least one error');\n\tthis.ase_errors = errors;\n\n\tVError.call(this, {\n\t    'cause': errors[0]\n\t}, 'first of %d error%s', errors.length, errors.length == 1 ? '' : 's');\n}\n\nmod_util.inherits(MultiError, VError);\nMultiError.prototype.name = 'MultiError';\n\nMultiError.prototype.errors = function me_errors()\n{\n\treturn (this.ase_errors.slice(0));\n};\n\n\n/*\n * See README.md for reference details.\n */\nfunction WError()\n{\n\tvar args, obj, parsed, options;\n\n\targs = Array.prototype.slice.call(arguments, 0);\n\tif (!(this instanceof WError)) {\n\t\tobj = Object.create(WError.prototype);\n\t\tWError.apply(obj, args);\n\t\treturn (obj);\n\t}\n\n\tparsed = parseConstructorArguments({\n\t    'argv': args,\n\t    'strict': false\n\t});\n\n\toptions = parsed.options;\n\toptions['skipCauseMessage'] = true;\n\tVError.call(this, options, '%s', parsed.shortmessage);\n\n\treturn (this);\n}\n\nmod_util.inherits(WError, VError);\nWError.prototype.name = 'WError';\n\nWError.prototype.toString = function we_toString()\n{\n\tvar str = (this.hasOwnProperty('name') && this.name ||\n\t\tthis.constructor.name || this.constructor.prototype.name);\n\tif (this.message)\n\t\tstr += ': ' + this.message;\n\tif (this.jse_cause && this.jse_cause.message)\n\t\tstr += '; caused by ' + this.jse_cause.toString();\n\n\treturn (str);\n};\n\n/*\n * For purely historical reasons, WError's cause() function allows you to set\n * the cause.\n */\nWError.prototype.cause = function we_cause(c)\n{\n\tif (mod_isError(c))\n\t\tthis.jse_cause = c;\n\n\treturn (this.jse_cause);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/verror/lib/verror.js\n");

/***/ })

};
;