"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stealthy-require";
exports.ids = ["vendor-chunks/stealthy-require"];
exports.modules = {

/***/ "(rsc)/./node_modules/stealthy-require/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/stealthy-require/lib/index.js ***!
  \****************************************************/
/***/ ((module) => {

eval("\n\nvar isNative = /\\.node$/;\n\nfunction forEach(obj, callback) {\n    for ( var key in obj ) {\n        if (!Object.prototype.hasOwnProperty.call(obj, key)) {\n            continue;\n        }\n        callback(key);\n    }\n}\n\nfunction assign(target, source) {\n    forEach(source, function (key) {\n        target[key] = source[key];\n    });\n    return target;\n}\n\nfunction clearCache(requireCache) {\n    forEach(requireCache, function (resolvedPath) {\n        if (!isNative.test(resolvedPath)) {\n            delete requireCache[resolvedPath];\n        }\n    });\n}\n\nmodule.exports = function (requireCache, callback, callbackForModulesToKeep, module) {\n\n    var originalCache = assign({}, requireCache);\n    clearCache(requireCache);\n\n    if (callbackForModulesToKeep) {\n\n        var originalModuleChildren = module.children ? module.children.slice() : false; // Creates a shallow copy of module.children\n\n        callbackForModulesToKeep();\n\n        // Lists the cache entries made by callbackForModulesToKeep()\n        var modulesToKeep = [];\n        forEach(requireCache, function (key) {\n            modulesToKeep.push(key);\n        });\n\n        // Discards the modules required in callbackForModulesToKeep()\n        clearCache(requireCache);\n\n        if (module.children) { // Only true for node.js\n            module.children = originalModuleChildren; // Removes last references to modules required in callbackForModulesToKeep() -> No memory leak\n        }\n\n        // Takes the cache entries of the original cache in case the modules where required before\n        for ( var i = 0; i < modulesToKeep.length; i+=1 ) {\n            if (originalCache[modulesToKeep[i]]) {\n                requireCache[modulesToKeep[i]] = originalCache[modulesToKeep[i]];\n            }\n        }\n\n    }\n\n    var freshModule = callback();\n\n    var stealthCache = callbackForModulesToKeep ? assign({}, requireCache) : false;\n\n    clearCache(requireCache);\n\n    if (callbackForModulesToKeep) {\n        // In case modules to keep were required inside the stealthy require for the first time, copy them to the restored cache\n        for ( var k = 0; k < modulesToKeep.length; k+=1 ) {\n            if (stealthCache[modulesToKeep[k]]) {\n                requireCache[modulesToKeep[k]] = stealthCache[modulesToKeep[k]];\n            }\n        }\n    }\n\n    assign(requireCache, originalCache);\n\n    return freshModule;\n\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/stealthy-require/lib/index.js\n");

/***/ })

};
;