import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Food for Soul - Transform Your Fitness Journey",
  description:
    "Professional workout plans designed by experts. Choose from personalized fitness programs to achieve your goals faster with our comprehensive training solutions.",
  keywords:
    "fitness, workout plans, personal training, exercise, health, gym, nutrition",
  authors: [{ name: "Food for Soul Team" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} ${inter.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
