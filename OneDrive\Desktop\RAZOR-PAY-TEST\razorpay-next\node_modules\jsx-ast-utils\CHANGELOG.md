# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v3.3.5](https://github.com/jsx-eslint/jsx-ast-utils/compare/v3.3.4...v3.3.5) - 2023-07-28

### Fixed

- [Fix] `extractProp`: support `JSXFragment` [`#132`](https://github.com/jsx-eslint/jsx-ast-utils/issues/132)

### Commits

- [Dev Deps] update `@babel/core`, `@babel/eslint-parser`, `@babel/parser`, `eslint` [`e5555d1`](https://github.com/jsx-eslint/jsx-ast-utils/commit/e5555d152dbe1e85324139756f637e65fe047976)
- [Tests] fix a test [`bde3ba9`](https://github.com/jsx-eslint/jsx-ast-utils/commit/bde3ba9c9dc294d5472eefa0b3f31ab0e1aed739)

## [v3.3.4](https://github.com/jsx-eslint/jsx-ast-utils/compare/v3.3.3...v3.3.4) - 2023-06-28

### Commits

- [Refactor] use `array.prototype.flat` `object.values` over `.reduce` [`bad51d0`](https://github.com/jsx-eslint/jsx-ast-utils/commit/bad51d062000ffdc19d925723a6515458318cf92)
- [meta] add `auto-changelog` [`af1de69`](https://github.com/jsx-eslint/jsx-ast-utils/commit/af1de693d1005144c13a75573631a670fa44547e)
- [Tests] add test for `import.meta` [`1d39f58`](https://github.com/jsx-eslint/jsx-ast-utils/commit/1d39f58c7bc6a89aa936e666b76e355c9436e854)
- [Dev Deps] update `@babel/core`, `@babel/eslint-parser`, `@babel/parser`, `aud`, `eslint`, `eslint-plugin-import` [`3baaf76`](https://github.com/jsx-eslint/jsx-ast-utils/commit/3baaf76c9c48ae85687ed23a3b9894c45927e081)
- [Fix] `TSNonNullExpression`: Handle function calls [`26cc3c4`](https://github.com/jsx-eslint/jsx-ast-utils/commit/26cc3c48165518b9fcdc9c625d918accc3e00107)
- [Dev Deps] update `eslint`, `@babel/core`, `@babel/parser`, `object.entries`, `object.fromentries` [`0e4f80c`](https://github.com/jsx-eslint/jsx-ast-utils/commit/0e4f80c2129c5b5ee50e4df2f25c5fb6728cbe8e)
- [Dev Deps] update `@babel/core`, `@babel/eslint-parser`, `@babel/parser`, `aud` [`b5427a6`](https://github.com/jsx-eslint/jsx-ast-utils/commit/b5427a65fac33e2264461b072b3edb078242dc85)
- [meta] run build in prepack, not prepublish [`a0f4f38`](https://github.com/jsx-eslint/jsx-ast-utils/commit/a0f4f383ddf82cd2914c7e594f356d9a78c80570)
- [Deps] update `array-includes` [`c479841`](https://github.com/jsx-eslint/jsx-ast-utils/commit/c479841d0b65188a3223541e885fa6286756a2c6)
- [Deps] update `object.assign` [`9685dce`](https://github.com/jsx-eslint/jsx-ast-utils/commit/9685dce823d71ac06fccd61d8aa8e13ba3d42f38)

<!-- auto-changelog-above -->

3.3.3 / 2022-08-08
==================
- [Fix] Mark ChainExpression as a noop (#109)
- [Deps] update `object.assign`
- [Dev Deps] update `@babel/core`, `@babel/eslint-parser`, `@babel/parser`, `eslint`

3.3.2 / 2022-07-06
==================
- [Fix] Handle `as` casts in TSNonNullExpression

3.3.1 / 2022-06-22
==================
- [Fix] `ArrayExpression`: handle sparse array (#117)
- [Deps] update `array-includes`
- [meta] move jest config to separate file
- [meta] use `npmignore` to autogenerate an npmignore file
- [Dev Deps] update `@babel/core`, `@babel/eslint-parser`, `@babel/parser`, `eslint`

3.3.0 / 2022-04-30
==================
- [New] add `JSXFragment`, `JSXText`; fix `JSXElement` to handle children
- [Dev Deps] update `@babel/core`, `@babel/parser`, `eslint`, `eslint-plugin-import`

3.2.2 / 2022-03-31
==================
- [Fix] `TSNonNullExpression`: handle computed MemberExpressions (#109)
- [Fix] avoid a crash in ChainExpressions in a TSAsExpression

3.2.1 / 2021-09-16
==================
- [patch] include project name in error logging (#113)
- [readme] update badges, URLs
- [Deps] update `array-includes`
- [meta] don‘t lint coverage results
- [meta] add GitHub org to FUNDING.yml
- [meta] add OpenCollective to FUNDING.yml
- [meta] run `aud` in `posttest`
- [meta] add Automatic Rebase and Require Allow Edits workflows
- [actions] use `node/install` instead of `node/run`; use `codecov` action
- [Tests] unpin `caniuse-lite`, since breaking change is fixed
- [Tests] pin `caniuse-lite`, due to breaking change in patch version
- [Tests] fix linting errors
- [Tests] migrate tests to Github Actions
- [Tests] stop using coveralls
- [Tests] skip failing fragment test in node 4
- [Dev Deps] update `@babel/core`, `@babel/parser`, `aud`, `eslint`, `eslint-plugin-import`, `object.entries`, `object.fromentries`

3.2.0 / 2020-12-16
==================
- [New] add support for fragment syntax (`<>`) (#108)
- [Fix] `TSNonNullExpression`: handle `ThisExpression`s (#108)
- [Deps] update `array-includes`, `object.assign`
- [Dev Deps] update `@babel/core`, `@babel/parser`, `eslint`, `eslint-config-airbnb-base`, `object.entries`, `object.fromentries`

3.1.0 / 2020-10-13
==================
- [New] add `TSNonNullExpression` (#105)
- [New] add `AssignmentExpression` (#106)
- [Dev Deps] update `eslint`

3.0.0 / 2020-10-06
==================
- [Breaking] Don't return node.start & node.end (#100)
- [Breaking] add `ChainExpression`; `CallExpression` now includes arguments (#102)
- [New] add `SequenceExpression` (#101)
- [Deps] update `object.assign`
- [Dev Deps] update `eslint`, `eslint-plugin-import`
- [Dev Deps] update `@babel/core`, `@babel/parser`, `eslint`, `eslint-plugin-import`
- [Tests] use proper `actual, expected` ordering for non-confusing failure messages

2.4.1 / 2020-06-11
==================
- [Fix] `expressions/TemplateLiteral`: use `.range[0]` instead of `.start`

2.4.0 / 2020-06-11
==================
- [New] Provide both range and start & end property on Node, support eslint v7 (#97)
- [Dev Deps] update `@babel/core`, `@babel/parser`, `eslint`, `eslint-config-airbnb-base`, `eslint-plugin-import`, `flow-parser`
- [meta] remove yarn registry from npmrc, so `npm publish` works

2.3.0 / 2020-05-24
==================
- [New] add nullish coalescing (#99)
- [New] add OptionalCallExpression (#99)
- [Deps] update `array-includes`
- [meta] add `safe-publish-latest`
- [Dev Deps] update `@babel/parser`, `babel-eslint`, `coveralls`, `eslint`, `eslint-config-airbnb-base`, `eslint-plugin-import`, `in-publish`, `object.entries`, `object.fromentries`, `rimraf`
- [Tests] on `node` `v14`; test all branches

2.2.3 / 2019-10-24
==================
- (fix) Fix crash on spread (#94)

2.2.2 / 2019-10-24
==================
- (improvement) Add support for retrieving props from a spread with object expression (#93)

2.2.1 / 2019-06-30
==================
- (improvement) Account for TypeCastExpression in the utils

2.2.0 / 2019-06-25
==================
- (fix) Fix getLiteralPropValue for TS-specific node types.
- (chore) upgrade dependencies.
- (improvement) Stop throwing errors when unknown AST nodes are encountered.
- (dev) CI changes.

2.1.0 / 2018-04-19
==================
- Fix undefined bug for template strings. #45
- Adding support for `objectRestSpread` within props #60
- Accommodate ExperimentalSpreadProperty in prop values #75
- Account for SpreadElement AST Nodes #76
- Support OptionalMemberExpression AST nodes #77
- Add support to Typescript's node types #72

2.0.1 / 2017-08-31
==================
- [fix] Add support for BindExpression


2.0.0 / 2017-07-07
==================
- [breaking] Remove undefined return from `propName` so it always returns a value.


1.4.1 / 2017-04-19
==================
- [fix] - Fixing fatal throw in `getPropValue` for `ArrowFunctionExpression`


1.4.0 / 2017-02-02
==================
- [new] Add eventHandlers and eventHandlersByType to API. These are the event names for DOM elements on JSX-using libraries such as React, inferno, and preact.


1.3.5 / 2016-12-14
==================
- [fix] Normalize literals "true" and "false" before converting to boolean in Literal prop value extractor.


1.3.4 / 2016-11-15
==================
- [fix] Recursively resolve JSXMemberExpression names for elementType. (i.e. `<Component.Render.Me />`). Fixes [#9](https://github.com/evcohen/jsx-ast-utils/issues/9)


1.3.3 / 2016-10-28
==================
- [fix] Add support for `ArrayExpression`.


1.3.2 / 2016-10-11
==================
- [fix] Add support for `UpdateExpression`.


1.3.1 / 2016-07-13
==================
- [fix] Add `JSXElement` to expression types to handle recursively extracting prop value.


1.3.0 / 2016-07-12
==================
- [new] Add support for `TaggedTemplateExpression`.


1.2.1 / 2016-06-15
==================
- [fix] Point to `lib` instead of `src` for root exports.


1.2.0 / 2016-06-15
==================
- [new] Export functions from root so they can be imported like the following: `require('jsx-ast-utils/{function}')`.


1.1.1 / 2016-06-12
==================
- [fix] Better support for expressions in `TemplateLiteral` extraction.


1.1.0 / 2016-06-10
==================
- [new] Support for namespaced element names.
- [new] Add `propName` to API to get correct name for prop.


1.0.1 / 2016-06-10
==================
- [fix] Return actual reserved words instead of string representations of them.


1.0.0 / 2016-06-09
==================
- Initial stable release
