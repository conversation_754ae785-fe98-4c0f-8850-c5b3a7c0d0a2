/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/forever-agent";
exports.ids = ["vendor-chunks/forever-agent"];
exports.modules = {

/***/ "(rsc)/./node_modules/forever-agent/index.js":
/*!*********************************************!*\
  !*** ./node_modules/forever-agent/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = ForeverAgent\nForeverAgent.SSL = ForeverAgentSSL\n\nvar util = __webpack_require__(/*! util */ \"util\")\n  , Agent = (__webpack_require__(/*! http */ \"http\").Agent)\n  , net = __webpack_require__(/*! net */ \"net\")\n  , tls = __webpack_require__(/*! tls */ \"tls\")\n  , AgentSSL = (__webpack_require__(/*! https */ \"https\").Agent)\n  \nfunction getConnectionName(host, port) {  \n  var name = ''\n  if (typeof host === 'string') {\n    name = host + ':' + port\n  } else {\n    // For node.js v012.0 and iojs-v1.5.1, host is an object. And any existing localAddress is part of the connection name.\n    name = host.host + ':' + host.port + ':' + (host.localAddress ? (host.localAddress + ':') : ':')\n  }\n  return name\n}    \n\nfunction ForeverAgent(options) {\n  var self = this\n  self.options = options || {}\n  self.requests = {}\n  self.sockets = {}\n  self.freeSockets = {}\n  self.maxSockets = self.options.maxSockets || Agent.defaultMaxSockets\n  self.minSockets = self.options.minSockets || ForeverAgent.defaultMinSockets\n  self.on('free', function(socket, host, port) {\n    var name = getConnectionName(host, port)\n\n    if (self.requests[name] && self.requests[name].length) {\n      self.requests[name].shift().onSocket(socket)\n    } else if (self.sockets[name].length < self.minSockets) {\n      if (!self.freeSockets[name]) self.freeSockets[name] = []\n      self.freeSockets[name].push(socket)\n      \n      // if an error happens while we don't use the socket anyway, meh, throw the socket away\n      var onIdleError = function() {\n        socket.destroy()\n      }\n      socket._onIdleError = onIdleError\n      socket.on('error', onIdleError)\n    } else {\n      // If there are no pending requests just destroy the\n      // socket and it will get removed from the pool. This\n      // gets us out of timeout issues and allows us to\n      // default to Connection:keep-alive.\n      socket.destroy()\n    }\n  })\n\n}\nutil.inherits(ForeverAgent, Agent)\n\nForeverAgent.defaultMinSockets = 5\n\n\nForeverAgent.prototype.createConnection = net.createConnection\nForeverAgent.prototype.addRequestNoreuse = Agent.prototype.addRequest\nForeverAgent.prototype.addRequest = function(req, host, port) {\n  var name = getConnectionName(host, port)\n  \n  if (typeof host !== 'string') {\n    var options = host\n    port = options.port\n    host = options.host\n  }\n\n  if (this.freeSockets[name] && this.freeSockets[name].length > 0 && !req.useChunkedEncodingByDefault) {\n    var idleSocket = this.freeSockets[name].pop()\n    idleSocket.removeListener('error', idleSocket._onIdleError)\n    delete idleSocket._onIdleError\n    req._reusedSocket = true\n    req.onSocket(idleSocket)\n  } else {\n    this.addRequestNoreuse(req, host, port)\n  }\n}\n\nForeverAgent.prototype.removeSocket = function(s, name, host, port) {\n  if (this.sockets[name]) {\n    var index = this.sockets[name].indexOf(s)\n    if (index !== -1) {\n      this.sockets[name].splice(index, 1)\n    }\n  } else if (this.sockets[name] && this.sockets[name].length === 0) {\n    // don't leak\n    delete this.sockets[name]\n    delete this.requests[name]\n  }\n  \n  if (this.freeSockets[name]) {\n    var index = this.freeSockets[name].indexOf(s)\n    if (index !== -1) {\n      this.freeSockets[name].splice(index, 1)\n      if (this.freeSockets[name].length === 0) {\n        delete this.freeSockets[name]\n      }\n    }\n  }\n\n  if (this.requests[name] && this.requests[name].length) {\n    // If we have pending requests and a socket gets closed a new one\n    // needs to be created to take over in the pool for the one that closed.\n    this.createSocket(name, host, port).emit('free')\n  }\n}\n\nfunction ForeverAgentSSL (options) {\n  ForeverAgent.call(this, options)\n}\nutil.inherits(ForeverAgentSSL, ForeverAgent)\n\nForeverAgentSSL.prototype.createConnection = createConnectionSSL\nForeverAgentSSL.prototype.addRequestNoreuse = AgentSSL.prototype.addRequest\n\nfunction createConnectionSSL (port, host, options) {\n  if (typeof port === 'object') {\n    options = port;\n  } else if (typeof host === 'object') {\n    options = host;\n  } else if (typeof options === 'object') {\n    options = options;\n  } else {\n    options = {};\n  }\n\n  if (typeof port === 'number') {\n    options.port = port;\n  }\n\n  if (typeof host === 'string') {\n    options.host = host;\n  }\n\n  return tls.connect(options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/forever-agent/index.js\n");

/***/ })

};
;