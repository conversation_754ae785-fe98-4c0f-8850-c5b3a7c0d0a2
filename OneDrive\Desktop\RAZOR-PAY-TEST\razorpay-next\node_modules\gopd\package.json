{"name": "gopd", "version": "1.0.1", "description": "`Object.getOwnPropertyDescriptor`, but accounts for IE's broken implementation.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/gopd.git"}, "keywords": ["ecmascript", "javascript", "getownpropertydescriptor", "property", "descriptor"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/gopd/issues"}, "homepage": "https://github.com/ljharb/gopd#readme", "dependencies": {"get-intrinsic": "^1.1.3"}, "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.1", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}