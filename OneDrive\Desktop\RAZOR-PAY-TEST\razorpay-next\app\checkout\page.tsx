"use client";
import * as React from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useRouter, useSearchParams } from "next/navigation";
import Script from "next/script";
import { LoaderCircle } from "lucide-react";
import { apiClient, authStorage } from "@/lib/api";

export default function Checkout() {
  const router = useRouter();
  const params = useSearchParams();
  const planId = params.get("planId");
  const durationId = params.get("durationId");
  const price = params.get("price");
  const [loading1, setLoading1] = React.useState(true);
  const [loading, setLoading] = React.useState(false);
  const [user, setUser] = React.useState<any>(null);
  const [couponCode] = React.useState("");
  const orderIdRef = React.useRef<string>();
  const [orderData, setOrderData] = React.useState<any>(null);

  const createOrderId = React.useCallback(
    async (token: string) => {
      try {
        const response = await apiClient.createOrder(
          {
            planId: planId!,
            durationId: durationId!,
            couponCode: couponCode || undefined,
          },
          token
        );
        // Store the full order data and use the razorpayOrderId
        setOrderData(response.data);
        orderIdRef.current = response.data.razorpayOrderId;
        setLoading1(false);
      } catch (error) {
        console.error("There was a problem creating the order:", error);
        setLoading1(false);
        alert("Failed to create order. Please try again.");
      }
    },
    [planId, durationId, couponCode]
  );

  React.useEffect(() => {
    const auth = authStorage.getAuth();
    if (!auth || !authStorage.isAuthenticated()) {
      router.replace("/login");
      return;
    }

    if (!planId || !durationId || !price) {
      router.replace("/");
      return;
    }

    setUser(auth);
    createOrderId(auth.accessToken);
  }, [planId, durationId, price, router, createOrderId]);

  const processPayment = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    const orderId = orderIdRef.current;

    if (!orderId) {
      alert("Order ID not found. Please try again.");
      setLoading(false);
      return;
    }

    try {
      const options = {
        key: orderData?.key || process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: orderData?.amount || parseFloat(price!) * 100,
        currency: orderData?.currency || "INR",
        name: "Workout Plan Payment",
        description: "Payment for workout plan subscription",
        order_id: orderId,
        handler: async function (response: any) {
          try {
            console.log("Payment response from Razorpay:", response);
            console.log("Order data:", orderData);
            console.log("User:", user);

            const verificationResult = await apiClient.verifyPayment(
              response.razorpay_order_id, // Use the Razorpay order ID from the payment response
              response.razorpay_payment_id,
              response.razorpay_signature,
              user.accessToken
            );

            console.log("Verification result:", verificationResult);

            if (verificationResult.success) {
              alert("Payment successful!");
              router.push("/success");
            } else {
              alert("Payment verification failed");
            }
          } catch (error) {
            console.error("Payment verification error:", error);
            console.error("Error details:", error);
            alert("Payment verification failed");
          }
        },
        theme: {
          color: "#3399cc",
        },
      };

      const paymentObject = new window.Razorpay(options);
      paymentObject.on("payment.failed", function (response: any) {
        alert(response.error.description);
      });
      setLoading(false);
      paymentObject.open();
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };
  if (loading1)
    return (
      <div className="container h-screen flex justify-center items-center">
        <LoaderCircle className=" animate-spin h-20 w-20 text-primary" />
      </div>
    );
  return (
    <>
      <Script
        id="razorpay-checkout-js"
        src="https://checkout.razorpay.com/v1/checkout.js"
      />

      <section className="container h-screen flex flex-col justify-center items-center gap-10">
        <h1 className="scroll-m-20 text-4xl font-extrabold tracking-tight">
          Checkout
        </h1>
        <Card className="max-w-[25rem] space-y-8">
          <CardHeader>
            <CardTitle className="my-4">Continue</CardTitle>
            <CardDescription>
              By clicking on pay you&apos;ll purchase your plan subscription of
              Rs {price}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={processPayment}>
              <Button className="w-full" type="submit">
                {loading ? "...loading" : "Pay"}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex">
            <p className="text-sm text-muted-foreground underline underline-offset-4">
              Please read the terms and conditions.
            </p>
          </CardFooter>
        </Card>
      </section>
    </>
  );
}
