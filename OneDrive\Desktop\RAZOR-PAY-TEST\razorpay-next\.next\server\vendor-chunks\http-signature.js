/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/http-signature";
exports.ids = ["vendor-chunks/http-signature"];
exports.modules = {

/***/ "(rsc)/./node_modules/http-signature/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/http-signature/lib/index.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright 2015 Joyent, Inc.\n\nvar parser = __webpack_require__(/*! ./parser */ \"(rsc)/./node_modules/http-signature/lib/parser.js\");\nvar signer = __webpack_require__(/*! ./signer */ \"(rsc)/./node_modules/http-signature/lib/signer.js\");\nvar verify = __webpack_require__(/*! ./verify */ \"(rsc)/./node_modules/http-signature/lib/verify.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/http-signature/lib/utils.js\");\n\n\n\n///--- API\n\nmodule.exports = {\n\n  parse: parser.parseRequest,\n  parseRequest: parser.parseRequest,\n\n  sign: signer.signRequest,\n  signRequest: signer.signRequest,\n  createSigner: signer.createSigner,\n  isSigner: signer.isSigner,\n\n  sshKeyToPEM: utils.sshKeyToPEM,\n  sshKeyFingerprint: utils.fingerprint,\n  pemToRsaSSHKey: utils.pemToRsaSSHKey,\n\n  verify: verify.verifySignature,\n  verifySignature: verify.verifySignature,\n  verifyHMAC: verify.verifyHMAC\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cC1zaWduYXR1cmUvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBOztBQUVBLGFBQWEsbUJBQU8sQ0FBQyxtRUFBVTtBQUMvQixhQUFhLG1CQUFPLENBQUMsbUVBQVU7QUFDL0IsYUFBYSxtQkFBTyxDQUFDLG1FQUFVO0FBQy9CLFlBQVksbUJBQU8sQ0FBQyxpRUFBUzs7OztBQUk3Qjs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Jhem9ycGF5LW5leHQvLi9ub2RlX21vZHVsZXMvaHR0cC1zaWduYXR1cmUvbGliL2luZGV4LmpzPzJiODkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMTUgSm95ZW50LCBJbmMuXG5cbnZhciBwYXJzZXIgPSByZXF1aXJlKCcuL3BhcnNlcicpO1xudmFyIHNpZ25lciA9IHJlcXVpcmUoJy4vc2lnbmVyJyk7XG52YXIgdmVyaWZ5ID0gcmVxdWlyZSgnLi92ZXJpZnknKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoJy4vdXRpbHMnKTtcblxuXG5cbi8vLy0tLSBBUElcblxubW9kdWxlLmV4cG9ydHMgPSB7XG5cbiAgcGFyc2U6IHBhcnNlci5wYXJzZVJlcXVlc3QsXG4gIHBhcnNlUmVxdWVzdDogcGFyc2VyLnBhcnNlUmVxdWVzdCxcblxuICBzaWduOiBzaWduZXIuc2lnblJlcXVlc3QsXG4gIHNpZ25SZXF1ZXN0OiBzaWduZXIuc2lnblJlcXVlc3QsXG4gIGNyZWF0ZVNpZ25lcjogc2lnbmVyLmNyZWF0ZVNpZ25lcixcbiAgaXNTaWduZXI6IHNpZ25lci5pc1NpZ25lcixcblxuICBzc2hLZXlUb1BFTTogdXRpbHMuc3NoS2V5VG9QRU0sXG4gIHNzaEtleUZpbmdlcnByaW50OiB1dGlscy5maW5nZXJwcmludCxcbiAgcGVtVG9Sc2FTU0hLZXk6IHV0aWxzLnBlbVRvUnNhU1NIS2V5LFxuXG4gIHZlcmlmeTogdmVyaWZ5LnZlcmlmeVNpZ25hdHVyZSxcbiAgdmVyaWZ5U2lnbmF0dXJlOiB2ZXJpZnkudmVyaWZ5U2lnbmF0dXJlLFxuICB2ZXJpZnlITUFDOiB2ZXJpZnkudmVyaWZ5SE1BQ1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http-signature/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http-signature/lib/parser.js":
/*!***************************************************!*\
  !*** ./node_modules/http-signature/lib/parser.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright 2012 Joyent, Inc.  All rights reserved.\n\nvar assert = __webpack_require__(/*! assert-plus */ \"(rsc)/./node_modules/assert-plus/assert.js\");\nvar util = __webpack_require__(/*! util */ \"util\");\nvar utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/http-signature/lib/utils.js\");\n\n\n\n///--- Globals\n\nvar HASH_ALGOS = utils.HASH_ALGOS;\nvar PK_ALGOS = utils.PK_ALGOS;\nvar HttpSignatureError = utils.HttpSignatureError;\nvar InvalidAlgorithmError = utils.InvalidAlgorithmError;\nvar validateAlgorithm = utils.validateAlgorithm;\n\nvar State = {\n  New: 0,\n  Params: 1\n};\n\nvar ParamsState = {\n  Name: 0,\n  Quote: 1,\n  Value: 2,\n  Comma: 3\n};\n\n\n///--- Specific Errors\n\n\nfunction ExpiredRequestError(message) {\n  HttpSignatureError.call(this, message, ExpiredRequestError);\n}\nutil.inherits(ExpiredRequestError, HttpSignatureError);\n\n\nfunction InvalidHeaderError(message) {\n  HttpSignatureError.call(this, message, InvalidHeaderError);\n}\nutil.inherits(InvalidHeaderError, HttpSignatureError);\n\n\nfunction InvalidParamsError(message) {\n  HttpSignatureError.call(this, message, InvalidParamsError);\n}\nutil.inherits(InvalidParamsError, HttpSignatureError);\n\n\nfunction MissingHeaderError(message) {\n  HttpSignatureError.call(this, message, MissingHeaderError);\n}\nutil.inherits(MissingHeaderError, HttpSignatureError);\n\nfunction StrictParsingError(message) {\n  HttpSignatureError.call(this, message, StrictParsingError);\n}\nutil.inherits(StrictParsingError, HttpSignatureError);\n\n///--- Exported API\n\nmodule.exports = {\n\n  /**\n   * Parses the 'Authorization' header out of an http.ServerRequest object.\n   *\n   * Note that this API will fully validate the Authorization header, and throw\n   * on any error.  It will not however check the signature, or the keyId format\n   * as those are specific to your environment.  You can use the options object\n   * to pass in extra constraints.\n   *\n   * As a response object you can expect this:\n   *\n   *     {\n   *       \"scheme\": \"Signature\",\n   *       \"params\": {\n   *         \"keyId\": \"foo\",\n   *         \"algorithm\": \"rsa-sha256\",\n   *         \"headers\": [\n   *           \"date\" or \"x-date\",\n   *           \"digest\"\n   *         ],\n   *         \"signature\": \"base64\"\n   *       },\n   *       \"signingString\": \"ready to be passed to crypto.verify()\"\n   *     }\n   *\n   * @param {Object} request an http.ServerRequest.\n   * @param {Object} options an optional options object with:\n   *                   - clockSkew: allowed clock skew in seconds (default 300).\n   *                   - headers: required header names (def: date or x-date)\n   *                   - algorithms: algorithms to support (default: all).\n   *                   - strict: should enforce latest spec parsing\n   *                             (default: false).\n   * @return {Object} parsed out object (see above).\n   * @throws {TypeError} on invalid input.\n   * @throws {InvalidHeaderError} on an invalid Authorization header error.\n   * @throws {InvalidParamsError} if the params in the scheme are invalid.\n   * @throws {MissingHeaderError} if the params indicate a header not present,\n   *                              either in the request headers from the params,\n   *                              or not in the params from a required header\n   *                              in options.\n   * @throws {StrictParsingError} if old attributes are used in strict parsing\n   *                              mode.\n   * @throws {ExpiredRequestError} if the value of date or x-date exceeds skew.\n   */\n  parseRequest: function parseRequest(request, options) {\n    assert.object(request, 'request');\n    assert.object(request.headers, 'request.headers');\n    if (options === undefined) {\n      options = {};\n    }\n    if (options.headers === undefined) {\n      options.headers = [request.headers['x-date'] ? 'x-date' : 'date'];\n    }\n    assert.object(options, 'options');\n    assert.arrayOfString(options.headers, 'options.headers');\n    assert.optionalFinite(options.clockSkew, 'options.clockSkew');\n\n    var authzHeaderName = options.authorizationHeaderName || 'authorization';\n\n    if (!request.headers[authzHeaderName]) {\n      throw new MissingHeaderError('no ' + authzHeaderName + ' header ' +\n                                   'present in the request');\n    }\n\n    options.clockSkew = options.clockSkew || 300;\n\n\n    var i = 0;\n    var state = State.New;\n    var substate = ParamsState.Name;\n    var tmpName = '';\n    var tmpValue = '';\n\n    var parsed = {\n      scheme: '',\n      params: {},\n      signingString: ''\n    };\n\n    var authz = request.headers[authzHeaderName];\n    for (i = 0; i < authz.length; i++) {\n      var c = authz.charAt(i);\n\n      switch (Number(state)) {\n\n      case State.New:\n        if (c !== ' ') parsed.scheme += c;\n        else state = State.Params;\n        break;\n\n      case State.Params:\n        switch (Number(substate)) {\n\n        case ParamsState.Name:\n          var code = c.charCodeAt(0);\n          // restricted name of A-Z / a-z\n          if ((code >= 0x41 && code <= 0x5a) || // A-Z\n              (code >= 0x61 && code <= 0x7a)) { // a-z\n            tmpName += c;\n          } else if (c === '=') {\n            if (tmpName.length === 0)\n              throw new InvalidHeaderError('bad param format');\n            substate = ParamsState.Quote;\n          } else {\n            throw new InvalidHeaderError('bad param format');\n          }\n          break;\n\n        case ParamsState.Quote:\n          if (c === '\"') {\n            tmpValue = '';\n            substate = ParamsState.Value;\n          } else {\n            throw new InvalidHeaderError('bad param format');\n          }\n          break;\n\n        case ParamsState.Value:\n          if (c === '\"') {\n            parsed.params[tmpName] = tmpValue;\n            substate = ParamsState.Comma;\n          } else {\n            tmpValue += c;\n          }\n          break;\n\n        case ParamsState.Comma:\n          if (c === ',') {\n            tmpName = '';\n            substate = ParamsState.Name;\n          } else {\n            throw new InvalidHeaderError('bad param format');\n          }\n          break;\n\n        default:\n          throw new Error('Invalid substate');\n        }\n        break;\n\n      default:\n        throw new Error('Invalid substate');\n      }\n\n    }\n\n    if (!parsed.params.headers || parsed.params.headers === '') {\n      if (request.headers['x-date']) {\n        parsed.params.headers = ['x-date'];\n      } else {\n        parsed.params.headers = ['date'];\n      }\n    } else {\n      parsed.params.headers = parsed.params.headers.split(' ');\n    }\n\n    // Minimally validate the parsed object\n    if (!parsed.scheme || parsed.scheme !== 'Signature')\n      throw new InvalidHeaderError('scheme was not \"Signature\"');\n\n    if (!parsed.params.keyId)\n      throw new InvalidHeaderError('keyId was not specified');\n\n    if (!parsed.params.algorithm)\n      throw new InvalidHeaderError('algorithm was not specified');\n\n    if (!parsed.params.signature)\n      throw new InvalidHeaderError('signature was not specified');\n\n    // Check the algorithm against the official list\n    parsed.params.algorithm = parsed.params.algorithm.toLowerCase();\n    try {\n      validateAlgorithm(parsed.params.algorithm);\n    } catch (e) {\n      if (e instanceof InvalidAlgorithmError)\n        throw (new InvalidParamsError(parsed.params.algorithm + ' is not ' +\n          'supported'));\n      else\n        throw (e);\n    }\n\n    // Build the signingString\n    for (i = 0; i < parsed.params.headers.length; i++) {\n      var h = parsed.params.headers[i].toLowerCase();\n      parsed.params.headers[i] = h;\n\n      if (h === 'request-line') {\n        if (!options.strict) {\n          /*\n           * We allow headers from the older spec drafts if strict parsing isn't\n           * specified in options.\n           */\n          parsed.signingString +=\n            request.method + ' ' + request.url + ' HTTP/' + request.httpVersion;\n        } else {\n          /* Strict parsing doesn't allow older draft headers. */\n          throw (new StrictParsingError('request-line is not a valid header ' +\n            'with strict parsing enabled.'));\n        }\n      } else if (h === '(request-target)') {\n        parsed.signingString +=\n          '(request-target): ' + request.method.toLowerCase() + ' ' +\n          request.url;\n      } else {\n        var value = request.headers[h];\n        if (value === undefined)\n          throw new MissingHeaderError(h + ' was not in the request');\n        parsed.signingString += h + ': ' + value;\n      }\n\n      if ((i + 1) < parsed.params.headers.length)\n        parsed.signingString += '\\n';\n    }\n\n    // Check against the constraints\n    var date;\n    if (request.headers.date || request.headers['x-date']) {\n        if (request.headers['x-date']) {\n          date = new Date(request.headers['x-date']);\n        } else {\n          date = new Date(request.headers.date);\n        }\n      var now = new Date();\n      var skew = Math.abs(now.getTime() - date.getTime());\n\n      if (skew > options.clockSkew * 1000) {\n        throw new ExpiredRequestError('clock skew of ' +\n                                      (skew / 1000) +\n                                      's was greater than ' +\n                                      options.clockSkew + 's');\n      }\n    }\n\n    options.headers.forEach(function (hdr) {\n      // Remember that we already checked any headers in the params\n      // were in the request, so if this passes we're good.\n      if (parsed.params.headers.indexOf(hdr.toLowerCase()) < 0)\n        throw new MissingHeaderError(hdr + ' was not a signed header');\n    });\n\n    if (options.algorithms) {\n      if (options.algorithms.indexOf(parsed.params.algorithm) === -1)\n        throw new InvalidParamsError(parsed.params.algorithm +\n                                     ' is not a supported algorithm');\n    }\n\n    parsed.algorithm = parsed.params.algorithm.toUpperCase();\n    parsed.keyId = parsed.params.keyId;\n    return parsed;\n  }\n\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http-signature/lib/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http-signature/lib/signer.js":
/*!***************************************************!*\
  !*** ./node_modules/http-signature/lib/signer.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright 2012 Joyent, Inc.  All rights reserved.\n\nvar assert = __webpack_require__(/*! assert-plus */ \"(rsc)/./node_modules/assert-plus/assert.js\");\nvar crypto = __webpack_require__(/*! crypto */ \"crypto\");\nvar http = __webpack_require__(/*! http */ \"http\");\nvar util = __webpack_require__(/*! util */ \"util\");\nvar sshpk = __webpack_require__(/*! sshpk */ \"(rsc)/./node_modules/sshpk/lib/index.js\");\nvar jsprim = __webpack_require__(/*! jsprim */ \"(rsc)/./node_modules/jsprim/lib/jsprim.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/http-signature/lib/utils.js\");\n\nvar sprintf = (__webpack_require__(/*! util */ \"util\").format);\n\nvar HASH_ALGOS = utils.HASH_ALGOS;\nvar PK_ALGOS = utils.PK_ALGOS;\nvar InvalidAlgorithmError = utils.InvalidAlgorithmError;\nvar HttpSignatureError = utils.HttpSignatureError;\nvar validateAlgorithm = utils.validateAlgorithm;\n\n///--- Globals\n\nvar AUTHZ_FMT =\n  'Signature keyId=\"%s\",algorithm=\"%s\",headers=\"%s\",signature=\"%s\"';\n\n///--- Specific Errors\n\nfunction MissingHeaderError(message) {\n  HttpSignatureError.call(this, message, MissingHeaderError);\n}\nutil.inherits(MissingHeaderError, HttpSignatureError);\n\nfunction StrictParsingError(message) {\n  HttpSignatureError.call(this, message, StrictParsingError);\n}\nutil.inherits(StrictParsingError, HttpSignatureError);\n\n/* See createSigner() */\nfunction RequestSigner(options) {\n  assert.object(options, 'options');\n\n  var alg = [];\n  if (options.algorithm !== undefined) {\n    assert.string(options.algorithm, 'options.algorithm');\n    alg = validateAlgorithm(options.algorithm);\n  }\n  this.rs_alg = alg;\n\n  /*\n   * RequestSigners come in two varieties: ones with an rs_signFunc, and ones\n   * with an rs_signer.\n   *\n   * rs_signFunc-based RequestSigners have to build up their entire signing\n   * string within the rs_lines array and give it to rs_signFunc as a single\n   * concat'd blob. rs_signer-based RequestSigners can add a line at a time to\n   * their signing state by using rs_signer.update(), thus only needing to\n   * buffer the hash function state and one line at a time.\n   */\n  if (options.sign !== undefined) {\n    assert.func(options.sign, 'options.sign');\n    this.rs_signFunc = options.sign;\n\n  } else if (alg[0] === 'hmac' && options.key !== undefined) {\n    assert.string(options.keyId, 'options.keyId');\n    this.rs_keyId = options.keyId;\n\n    if (typeof (options.key) !== 'string' && !Buffer.isBuffer(options.key))\n      throw (new TypeError('options.key for HMAC must be a string or Buffer'));\n\n    /*\n     * Make an rs_signer for HMACs, not a rs_signFunc -- HMACs digest their\n     * data in chunks rather than requiring it all to be given in one go\n     * at the end, so they are more similar to signers than signFuncs.\n     */\n    this.rs_signer = crypto.createHmac(alg[1].toUpperCase(), options.key);\n    this.rs_signer.sign = function () {\n      var digest = this.digest('base64');\n      return ({\n        hashAlgorithm: alg[1],\n        toString: function () { return (digest); }\n      });\n    };\n\n  } else if (options.key !== undefined) {\n    var key = options.key;\n    if (typeof (key) === 'string' || Buffer.isBuffer(key))\n      key = sshpk.parsePrivateKey(key);\n\n    assert.ok(sshpk.PrivateKey.isPrivateKey(key, [1, 2]),\n      'options.key must be a sshpk.PrivateKey');\n    this.rs_key = key;\n\n    assert.string(options.keyId, 'options.keyId');\n    this.rs_keyId = options.keyId;\n\n    if (!PK_ALGOS[key.type]) {\n      throw (new InvalidAlgorithmError(key.type.toUpperCase() + ' type ' +\n        'keys are not supported'));\n    }\n\n    if (alg[0] !== undefined && key.type !== alg[0]) {\n      throw (new InvalidAlgorithmError('options.key must be a ' +\n        alg[0].toUpperCase() + ' key, was given a ' +\n        key.type.toUpperCase() + ' key instead'));\n    }\n\n    this.rs_signer = key.createSign(alg[1]);\n\n  } else {\n    throw (new TypeError('options.sign (func) or options.key is required'));\n  }\n\n  this.rs_headers = [];\n  this.rs_lines = [];\n}\n\n/**\n * Adds a header to be signed, with its value, into this signer.\n *\n * @param {String} header\n * @param {String} value\n * @return {String} value written\n */\nRequestSigner.prototype.writeHeader = function (header, value) {\n  assert.string(header, 'header');\n  header = header.toLowerCase();\n  assert.string(value, 'value');\n\n  this.rs_headers.push(header);\n\n  if (this.rs_signFunc) {\n    this.rs_lines.push(header + ': ' + value);\n\n  } else {\n    var line = header + ': ' + value;\n    if (this.rs_headers.length > 0)\n      line = '\\n' + line;\n    this.rs_signer.update(line);\n  }\n\n  return (value);\n};\n\n/**\n * Adds a default Date header, returning its value.\n *\n * @return {String}\n */\nRequestSigner.prototype.writeDateHeader = function () {\n  return (this.writeHeader('date', jsprim.rfc1123(new Date())));\n};\n\n/**\n * Adds the request target line to be signed.\n *\n * @param {String} method, HTTP method (e.g. 'get', 'post', 'put')\n * @param {String} path\n */\nRequestSigner.prototype.writeTarget = function (method, path) {\n  assert.string(method, 'method');\n  assert.string(path, 'path');\n  method = method.toLowerCase();\n  this.writeHeader('(request-target)', method + ' ' + path);\n};\n\n/**\n * Calculate the value for the Authorization header on this request\n * asynchronously.\n *\n * @param {Func} callback (err, authz)\n */\nRequestSigner.prototype.sign = function (cb) {\n  assert.func(cb, 'callback');\n\n  if (this.rs_headers.length < 1)\n    throw (new Error('At least one header must be signed'));\n\n  var alg, authz;\n  if (this.rs_signFunc) {\n    var data = this.rs_lines.join('\\n');\n    var self = this;\n    this.rs_signFunc(data, function (err, sig) {\n      if (err) {\n        cb(err);\n        return;\n      }\n      try {\n        assert.object(sig, 'signature');\n        assert.string(sig.keyId, 'signature.keyId');\n        assert.string(sig.algorithm, 'signature.algorithm');\n        assert.string(sig.signature, 'signature.signature');\n        alg = validateAlgorithm(sig.algorithm);\n\n        authz = sprintf(AUTHZ_FMT,\n          sig.keyId,\n          sig.algorithm,\n          self.rs_headers.join(' '),\n          sig.signature);\n      } catch (e) {\n        cb(e);\n        return;\n      }\n      cb(null, authz);\n    });\n\n  } else {\n    try {\n      var sigObj = this.rs_signer.sign();\n    } catch (e) {\n      cb(e);\n      return;\n    }\n    alg = (this.rs_alg[0] || this.rs_key.type) + '-' + sigObj.hashAlgorithm;\n    var signature = sigObj.toString();\n    authz = sprintf(AUTHZ_FMT,\n      this.rs_keyId,\n      alg,\n      this.rs_headers.join(' '),\n      signature);\n    cb(null, authz);\n  }\n};\n\n///--- Exported API\n\nmodule.exports = {\n  /**\n   * Identifies whether a given object is a request signer or not.\n   *\n   * @param {Object} object, the object to identify\n   * @returns {Boolean}\n   */\n  isSigner: function (obj) {\n    if (typeof (obj) === 'object' && obj instanceof RequestSigner)\n      return (true);\n    return (false);\n  },\n\n  /**\n   * Creates a request signer, used to asynchronously build a signature\n   * for a request (does not have to be an http.ClientRequest).\n   *\n   * @param {Object} options, either:\n   *                   - {String} keyId\n   *                   - {String|Buffer} key\n   *                   - {String} algorithm (optional, required for HMAC)\n   *                 or:\n   *                   - {Func} sign (data, cb)\n   * @return {RequestSigner}\n   */\n  createSigner: function createSigner(options) {\n    return (new RequestSigner(options));\n  },\n\n  /**\n   * Adds an 'Authorization' header to an http.ClientRequest object.\n   *\n   * Note that this API will add a Date header if it's not already set. Any\n   * other headers in the options.headers array MUST be present, or this\n   * will throw.\n   *\n   * You shouldn't need to check the return type; it's just there if you want\n   * to be pedantic.\n   *\n   * The optional flag indicates whether parsing should use strict enforcement\n   * of the version draft-cavage-http-signatures-04 of the spec or beyond.\n   * The default is to be loose and support\n   * older versions for compatibility.\n   *\n   * @param {Object} request an instance of http.ClientRequest.\n   * @param {Object} options signing parameters object:\n   *                   - {String} keyId required.\n   *                   - {String} key required (either a PEM or HMAC key).\n   *                   - {Array} headers optional; defaults to ['date'].\n   *                   - {String} algorithm optional (unless key is HMAC);\n   *                              default is the same as the sshpk default\n   *                              signing algorithm for the type of key given\n   *                   - {String} httpVersion optional; defaults to '1.1'.\n   *                   - {Boolean} strict optional; defaults to 'false'.\n   * @return {Boolean} true if Authorization (and optionally Date) were added.\n   * @throws {TypeError} on bad parameter types (input).\n   * @throws {InvalidAlgorithmError} if algorithm was bad or incompatible with\n   *                                 the given key.\n   * @throws {sshpk.KeyParseError} if key was bad.\n   * @throws {MissingHeaderError} if a header to be signed was specified but\n   *                              was not present.\n   */\n  signRequest: function signRequest(request, options) {\n    assert.object(request, 'request');\n    assert.object(options, 'options');\n    assert.optionalString(options.algorithm, 'options.algorithm');\n    assert.string(options.keyId, 'options.keyId');\n    assert.optionalArrayOfString(options.headers, 'options.headers');\n    assert.optionalString(options.httpVersion, 'options.httpVersion');\n\n    if (!request.getHeader('Date'))\n      request.setHeader('Date', jsprim.rfc1123(new Date()));\n    if (!options.headers)\n      options.headers = ['date'];\n    if (!options.httpVersion)\n      options.httpVersion = '1.1';\n\n    var alg = [];\n    if (options.algorithm) {\n      options.algorithm = options.algorithm.toLowerCase();\n      alg = validateAlgorithm(options.algorithm);\n    }\n\n    var i;\n    var stringToSign = '';\n    for (i = 0; i < options.headers.length; i++) {\n      if (typeof (options.headers[i]) !== 'string')\n        throw new TypeError('options.headers must be an array of Strings');\n\n      var h = options.headers[i].toLowerCase();\n\n      if (h === 'request-line') {\n        if (!options.strict) {\n          /**\n           * We allow headers from the older spec drafts if strict parsing isn't\n           * specified in options.\n           */\n          stringToSign +=\n            request.method + ' ' + request.path + ' HTTP/' +\n            options.httpVersion;\n        } else {\n          /* Strict parsing doesn't allow older draft headers. */\n          throw (new StrictParsingError('request-line is not a valid header ' +\n            'with strict parsing enabled.'));\n        }\n      } else if (h === '(request-target)') {\n        stringToSign +=\n          '(request-target): ' + request.method.toLowerCase() + ' ' +\n          request.path;\n      } else {\n        var value = request.getHeader(h);\n        if (value === undefined || value === '') {\n          throw new MissingHeaderError(h + ' was not in the request');\n        }\n        stringToSign += h + ': ' + value;\n      }\n\n      if ((i + 1) < options.headers.length)\n        stringToSign += '\\n';\n    }\n\n    /* This is just for unit tests. */\n    if (request.hasOwnProperty('_stringToSign')) {\n      request._stringToSign = stringToSign;\n    }\n\n    var signature;\n    if (alg[0] === 'hmac') {\n      if (typeof (options.key) !== 'string' && !Buffer.isBuffer(options.key))\n        throw (new TypeError('options.key must be a string or Buffer'));\n\n      var hmac = crypto.createHmac(alg[1].toUpperCase(), options.key);\n      hmac.update(stringToSign);\n      signature = hmac.digest('base64');\n\n    } else {\n      var key = options.key;\n      if (typeof (key) === 'string' || Buffer.isBuffer(key))\n        key = sshpk.parsePrivateKey(options.key);\n\n      assert.ok(sshpk.PrivateKey.isPrivateKey(key, [1, 2]),\n        'options.key must be a sshpk.PrivateKey');\n\n      if (!PK_ALGOS[key.type]) {\n        throw (new InvalidAlgorithmError(key.type.toUpperCase() + ' type ' +\n          'keys are not supported'));\n      }\n\n      if (alg[0] !== undefined && key.type !== alg[0]) {\n        throw (new InvalidAlgorithmError('options.key must be a ' +\n          alg[0].toUpperCase() + ' key, was given a ' +\n          key.type.toUpperCase() + ' key instead'));\n      }\n\n      var signer = key.createSign(alg[1]);\n      signer.update(stringToSign);\n      var sigObj = signer.sign();\n      if (!HASH_ALGOS[sigObj.hashAlgorithm]) {\n        throw (new InvalidAlgorithmError(sigObj.hashAlgorithm.toUpperCase() +\n          ' is not a supported hash algorithm'));\n      }\n      options.algorithm = key.type + '-' + sigObj.hashAlgorithm;\n      signature = sigObj.toString();\n      assert.notStrictEqual(signature, '', 'empty signature produced');\n    }\n\n    var authzHeaderName = options.authorizationHeaderName || 'Authorization';\n\n    request.setHeader(authzHeaderName, sprintf(AUTHZ_FMT,\n                                               options.keyId,\n                                               options.algorithm,\n                                               options.headers.join(' '),\n                                               signature));\n\n    return true;\n  }\n\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http-signature/lib/signer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http-signature/lib/utils.js":
/*!**************************************************!*\
  !*** ./node_modules/http-signature/lib/utils.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright 2012 Joyent, Inc.  All rights reserved.\n\nvar assert = __webpack_require__(/*! assert-plus */ \"(rsc)/./node_modules/assert-plus/assert.js\");\nvar sshpk = __webpack_require__(/*! sshpk */ \"(rsc)/./node_modules/sshpk/lib/index.js\");\nvar util = __webpack_require__(/*! util */ \"util\");\n\nvar HASH_ALGOS = {\n  'sha1': true,\n  'sha256': true,\n  'sha512': true\n};\n\nvar PK_ALGOS = {\n  'rsa': true,\n  'dsa': true,\n  'ecdsa': true\n};\n\nfunction HttpSignatureError(message, caller) {\n  if (Error.captureStackTrace)\n    Error.captureStackTrace(this, caller || HttpSignatureError);\n\n  this.message = message;\n  this.name = caller.name;\n}\nutil.inherits(HttpSignatureError, Error);\n\nfunction InvalidAlgorithmError(message) {\n  HttpSignatureError.call(this, message, InvalidAlgorithmError);\n}\nutil.inherits(InvalidAlgorithmError, HttpSignatureError);\n\nfunction validateAlgorithm(algorithm) {\n  var alg = algorithm.toLowerCase().split('-');\n\n  if (alg.length !== 2) {\n    throw (new InvalidAlgorithmError(alg[0].toUpperCase() + ' is not a ' +\n      'valid algorithm'));\n  }\n\n  if (alg[0] !== 'hmac' && !PK_ALGOS[alg[0]]) {\n    throw (new InvalidAlgorithmError(alg[0].toUpperCase() + ' type keys ' +\n      'are not supported'));\n  }\n\n  if (!HASH_ALGOS[alg[1]]) {\n    throw (new InvalidAlgorithmError(alg[1].toUpperCase() + ' is not a ' +\n      'supported hash algorithm'));\n  }\n\n  return (alg);\n}\n\n///--- API\n\nmodule.exports = {\n\n  HASH_ALGOS: HASH_ALGOS,\n  PK_ALGOS: PK_ALGOS,\n\n  HttpSignatureError: HttpSignatureError,\n  InvalidAlgorithmError: InvalidAlgorithmError,\n\n  validateAlgorithm: validateAlgorithm,\n\n  /**\n   * Converts an OpenSSH public key (rsa only) to a PKCS#8 PEM file.\n   *\n   * The intent of this module is to interoperate with OpenSSL only,\n   * specifically the node crypto module's `verify` method.\n   *\n   * @param {String} key an OpenSSH public key.\n   * @return {String} PEM encoded form of the RSA public key.\n   * @throws {TypeError} on bad input.\n   * @throws {Error} on invalid ssh key formatted data.\n   */\n  sshKeyToPEM: function sshKeyToPEM(key) {\n    assert.string(key, 'ssh_key');\n\n    var k = sshpk.parseKey(key, 'ssh');\n    return (k.toString('pem'));\n  },\n\n\n  /**\n   * Generates an OpenSSH fingerprint from an ssh public key.\n   *\n   * @param {String} key an OpenSSH public key.\n   * @return {String} key fingerprint.\n   * @throws {TypeError} on bad input.\n   * @throws {Error} if what you passed doesn't look like an ssh public key.\n   */\n  fingerprint: function fingerprint(key) {\n    assert.string(key, 'ssh_key');\n\n    var k = sshpk.parseKey(key, 'ssh');\n    return (k.fingerprint('md5').toString('hex'));\n  },\n\n  /**\n   * Converts a PKGCS#8 PEM file to an OpenSSH public key (rsa)\n   *\n   * The reverse of the above function.\n   */\n  pemToRsaSSHKey: function pemToRsaSSHKey(pem, comment) {\n    assert.equal('string', typeof (pem), 'typeof pem');\n\n    var k = sshpk.parseKey(pem, 'pem');\n    k.comment = comment;\n    return (k.toString('ssh'));\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http-signature/lib/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http-signature/lib/verify.js":
/*!***************************************************!*\
  !*** ./node_modules/http-signature/lib/verify.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright 2015 Joyent, Inc.\n\nvar assert = __webpack_require__(/*! assert-plus */ \"(rsc)/./node_modules/assert-plus/assert.js\");\nvar crypto = __webpack_require__(/*! crypto */ \"crypto\");\nvar sshpk = __webpack_require__(/*! sshpk */ \"(rsc)/./node_modules/sshpk/lib/index.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/http-signature/lib/utils.js\");\n\nvar HASH_ALGOS = utils.HASH_ALGOS;\nvar PK_ALGOS = utils.PK_ALGOS;\nvar InvalidAlgorithmError = utils.InvalidAlgorithmError;\nvar HttpSignatureError = utils.HttpSignatureError;\nvar validateAlgorithm = utils.validateAlgorithm;\n\n///--- Exported API\n\nmodule.exports = {\n  /**\n   * Verify RSA/DSA signature against public key.  You are expected to pass in\n   * an object that was returned from `parse()`.\n   *\n   * @param {Object} parsedSignature the object you got from `parse`.\n   * @param {String} pubkey RSA/DSA private key PEM.\n   * @return {Boolean} true if valid, false otherwise.\n   * @throws {TypeError} if you pass in bad arguments.\n   * @throws {InvalidAlgorithmError}\n   */\n  verifySignature: function verifySignature(parsedSignature, pubkey) {\n    assert.object(parsedSignature, 'parsedSignature');\n    if (typeof (pubkey) === 'string' || Buffer.isBuffer(pubkey))\n      pubkey = sshpk.parseKey(pubkey);\n    assert.ok(sshpk.Key.isKey(pubkey, [1, 1]), 'pubkey must be a sshpk.Key');\n\n    var alg = validateAlgorithm(parsedSignature.algorithm);\n    if (alg[0] === 'hmac' || alg[0] !== pubkey.type)\n      return (false);\n\n    var v = pubkey.createVerify(alg[1]);\n    v.update(parsedSignature.signingString);\n    return (v.verify(parsedSignature.params.signature, 'base64'));\n  },\n\n  /**\n   * Verify HMAC against shared secret.  You are expected to pass in an object\n   * that was returned from `parse()`.\n   *\n   * @param {Object} parsedSignature the object you got from `parse`.\n   * @param {String} secret HMAC shared secret.\n   * @return {Boolean} true if valid, false otherwise.\n   * @throws {TypeError} if you pass in bad arguments.\n   * @throws {InvalidAlgorithmError}\n   */\n  verifyHMAC: function verifyHMAC(parsedSignature, secret) {\n    assert.object(parsedSignature, 'parsedHMAC');\n    assert.string(secret, 'secret');\n\n    var alg = validateAlgorithm(parsedSignature.algorithm);\n    if (alg[0] !== 'hmac')\n      return (false);\n\n    var hashAlg = alg[1].toUpperCase();\n\n    var hmac = crypto.createHmac(hashAlg, secret);\n    hmac.update(parsedSignature.signingString);\n\n    /*\n     * Now double-hash to avoid leaking timing information - there's\n     * no easy constant-time compare in JS, so we use this approach\n     * instead. See for more info:\n     * https://www.isecpartners.com/blog/2011/february/double-hmac-\n     * verification.aspx\n     */\n    var h1 = crypto.createHmac(hashAlg, secret);\n    h1.update(hmac.digest());\n    h1 = h1.digest();\n    var h2 = crypto.createHmac(hashAlg, secret);\n    h2.update(new Buffer(parsedSignature.params.signature, 'base64'));\n    h2 = h2.digest();\n\n    /* Node 0.8 returns strings from .digest(). */\n    if (typeof (h1) === 'string')\n      return (h1 === h2);\n    /* And node 0.10 lacks the .equals() method on Buffers. */\n    if (Buffer.isBuffer(h1) && !h1.equals)\n      return (h1.toString('binary') === h2.toString('binary'));\n\n    return (h1.equals(h2));\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http-signature/lib/verify.js\n");

/***/ })

};
;